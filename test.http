### G<PERSON>ondheidscheck (Local Docker)
GET http://localhost:8082/api/health

### Gezondheidscheck (Production)
GET https://dklemailservice.onrender.com/api/health

### Login
POST http://localhost:8080/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "wachtwoord": "admin123"
}

### Gebruikersprofiel ophalen
GET http://localhost:8080/api/auth/profile
Authorization: Bearer {{auth_token}}

### Wachtwoord wijzigen
POST http://localhost:8080/api/auth/reset-password
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "huidig_wachtwoord": "admin123",
  "nieuw_wachtwoord": "nieuw_wachtwoord"
}

### Uitloggen
POST http://localhost:8080/api/auth/logout

### Email metrics ophalen (admin)
GET http://localhost:8080/api/admin/metrics/email
Authorization: Bearer {{auth_token}}

### Rate limit metrics ophalen (admin)
GET http://localhost:8080/api/admin/metrics/rate-limits
Authorization: Bearer {{auth_token}}

### Contact email versturen
POST http://localhost:8080/api/contact-email
Content-Type: application/json

{
  "naam": "Test Gebruiker",
  "email": "<EMAIL>",
  "bericht": "Dit is een testbericht",
  "privacy_akkoord": true
}

### Aanmelding email versturen
POST http://localhost:8080/api/aanmelding-email
Content-Type: application/json

{
  "naam": "Test Deelnemer",
  "email": "<EMAIL>",
  "geboortedatum": "1990-01-01",
  "afstand": "10km",
  "privacy_akkoord": true
}

### ============================================
### STEPS API - Stappen Tracking
### ============================================

### [PUBLIC] Totaal aantal gelopen stappen (ALLE deelnemers)
GET http://localhost:8080/api/total-steps

### [PUBLIC] Totaal aantal stappen voor specifiek jaar
GET http://localhost:8080/api/total-steps?year=2025

### [PUBLIC] Fondsverdeling over routes ophalen
GET http://localhost:8080/api/funds-distribution

### Participant: Eigen dashboard ophalen
GET http://localhost:8080/api/participant/dashboard
Authorization: Bearer {{auth_token}}

### Participant: Eigen stappen bijwerken (+1000 stappen)
POST http://localhost:8080/api/steps
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "steps": 1000
}

### Admin: Stappen bijwerken voor specifieke deelnemer
POST http://localhost:8080/api/steps/{{participant_id}}
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "steps": 500
}

### Admin: Dashboard ophalen voor specifieke deelnemer
GET http://localhost:8080/api/participant/{{participant_id}}/dashboard
Authorization: Bearer {{auth_token}}

### Admin: Route funds ophalen
GET http://localhost:8080/api/steps/admin/route-funds
Authorization: Bearer {{auth_token}}

### Admin: Route fund aanmaken
POST http://localhost:8080/api/steps/admin/route-funds
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "route": "5 KM",
  "amount": 40
}

### Admin: Route fund bijwerken
PUT http://localhost:8080/api/steps/admin/route-funds/6%20KM
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "route": "6 KM",
  "amount": 60
}

### Admin: Route fund verwijderen
DELETE http://localhost:8080/api/steps/admin/route-funds/5%20KM
Authorization: Bearer {{auth_token}}

### ============================================
### EVENTS API - Event Management & Tracking
### ============================================

### [PUBLIC] Haal actief event op (Local Docker)
GET http://localhost:8082/api/events/active

### [PUBLIC] Haal actief event op (Production)
GET https://dklemailservice.onrender.com/api/events/active

### [PUBLIC] Haal alle events op
GET http://localhost:8082/api/events

### [PUBLIC] Haal alleen actieve events op
GET http://localhost:8082/api/events?active_only=true

### [PUBLIC] Haal events op met paginatie
GET http://localhost:8082/api/events?limit=10&offset=0

### [PUBLIC] Haal specifiek event op (vervang met echte UUID na aanmaken)
GET http://localhost:8082/api/events/{{event_id}}

### Admin: Nieuw event aanmaken
POST http://localhost:8082/api/events
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "name": "De Koninklijke Loop 2026",
  "description": "Test event voor development",
  "start_time": "2026-05-15T09:00:00Z",
  "end_time": "2026-05-15T16:00:00Z",
  "status": "upcoming",
  "geofences": [
    {
      "type": "start",
      "lat": 52.0907,
      "long": 5.1214,
      "radius": 50,
      "name": "Start Locatie"
    },
    {
      "type": "checkpoint",
      "lat": 52.0950,
      "long": 5.1300,
      "radius": 30,
      "name": "Checkpoint 5KM"
    },
    {
      "type": "finish",
      "lat": 52.0907,
      "long": 5.1214,
      "radius": 50,
      "name": "Finish Locatie"
    }
  ],
  "event_config": {
    "minStepsInterval": 10,
    "requireGeofenceCheckin": true,
    "distanceThreshold": 100,
    "accuracyLevel": "balanced"
  },
  "is_active": true
}

### Admin: Event bijwerken
PUT http://localhost:8082/api/events/{{event_id}}
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "name": "De Koninklijke Loop 2026 - UPDATED",
  "status": "active"
}

### Admin: Event status wijzigen
PUT http://localhost:8082/api/events/{{event_id}}
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "status": "completed"
}

### Admin: Event deactiveren
PUT http://localhost:8082/api/events/{{event_id}}
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "is_active": false
}

### Admin: Event participants ophalen
GET http://localhost:8082/api/events/{{event_id}}/participants
Authorization: Bearer {{auth_token}}

### Admin: Event verwijderen
DELETE http://localhost:8082/api/events/{{event_id}}
Authorization: Bearer {{auth_token}}

### ============================================
### EVENT TRACKING - Mobile App Use Case
### ============================================

### Step 1: Mobile app haalt actief event op (PUBLIC - Production)
GET https://dklemailservice.onrender.com/api/events/active

### Step 2: Mobile app checkt geofences (client-side met event.geofences data)
### Step 3: Mobile app update stappen wanneer in geofence (zie STEPS API hierboven)