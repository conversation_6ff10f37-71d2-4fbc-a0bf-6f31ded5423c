package main

import (
	"dklautomationgo/config"
	"dklautomationgo/database"
	"dklautomationgo/handlers"
	"dklautomationgo/logger"
	"dklautomationgo/repository"
	"dklautomationgo/services"
	"fmt"
	"net/http/httptest"
	"os"
	"os/signal"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/joho/godotenv"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// ValidateEnv controleert of alle benodigde omgevingsvariabelen zijn ingesteld
func ValidateEnv() error {
	required := []string{
		// Algemene SMTP configuratie
		"SMTP_HOST",
		"SMTP_USER",
		"SMTP_PASSWORD",
		"SMTP_FROM",

		// Registratie SMTP configuratie
		"REGISTRATION_SMTP_HOST",
		"REGISTRATION_SMTP_USER",
		"REGISTRATION_SMTP_PASSWORD",
		"REGISTRATION_SMTP_FROM",

		// Email adressen
		"ADMIN_EMAIL",
		"REGISTRATION_EMAIL",

		// Database configuratie
		"DB_HOST",
		"DB_PORT",
		"DB_USER",
		"DB_PASSWORD",
		"DB_NAME",
		"DB_SSL_MODE",

		// JWT configuratie
		"JWT_SECRET",
	}

	for _, env := range required {
		if os.Getenv(env) == "" {
			return fmt.Errorf("ontbrekende omgevingsvariabele: %s", env)
		}
	}

	// Controleer email fetcher configuratie indien ingeschakeld
	if os.Getenv("DISABLE_AUTO_EMAIL_FETCH") != "true" {
		emailFetcherVars := []string{
			"INFO_EMAIL",
			"INFO_EMAIL_PASSWORD",
			"INSCHRIJVING_EMAIL",
			"INSCHRIJVING_EMAIL_PASSWORD",
		}

		missingVars := []string{}
		for _, env := range emailFetcherVars {
			if os.Getenv(env) == "" {
				missingVars = append(missingVars, env)
			}
		}

		if len(missingVars) > 0 {
			logger.Warn("Email fetcher credentials missing, some accounts will not be configured",
				"missing_vars", strings.Join(missingVars, ", "))
		}
	}

	// Whisky for Charity configuratie is optioneel
	wfcConfigured := os.Getenv("WFC_SMTP_HOST") != "" &&
		os.Getenv("WFC_SMTP_USER") != "" &&
		os.Getenv("WFC_SMTP_PASSWORD") != "" &&
		os.Getenv("WFC_SMTP_FROM") != ""

	if wfcConfigured {
		logger.Info("Whisky for Charity SMTP configuratie gevonden")
	} else {
		logger.Info("Whisky for Charity SMTP configuratie niet gevonden, deze functionaliteit is uitgeschakeld")
	}

	// Newsletter configuratie (optioneel)
	enableNewsletter := os.Getenv("ENABLE_NEWSLETTER") == "true"
	if enableNewsletter {
		if os.Getenv("NEWSLETTER_SOURCES") == "" {
			logger.Warn("ENABLE_NEWSLETTER is true maar NEWSLETTER_SOURCES is leeg")
		}
	}

	return nil
}

func main() {
	// Laad .env bestand als het bestaat
	if err := godotenv.Load(); err != nil && !os.IsNotExist(err) {
		logger.Warn("Kon .env bestand niet laden", "error", err)
	}

	// Initialiseer de logger met niveau uit omgevingsvariabele of standaard INFO
	logLevel := os.Getenv("LOG_LEVEL")
	if logLevel == "" {
		logLevel = logger.InfoLevel
	}
	logger.Setup(logLevel)
	defer logger.Sync()

	// Debug: Print alle omgevingsvariabelen alleen bij DEBUG logniveau
	if strings.ToUpper(logLevel) == logger.DebugLevel {
		logger.Debug("Omgevingsvariabelen debug:")
		for _, env := range []string{
			"DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_NAME", "DB_SSL_MODE",
			"SMTP_HOST", "SMTP_PORT", "SMTP_USER", "SMTP_PASSWORD", "SMTP_FROM",
			"REGISTRATION_SMTP_HOST", "REGISTRATION_SMTP_PORT", "REGISTRATION_SMTP_USER",
			"REGISTRATION_SMTP_PASSWORD", "REGISTRATION_SMTP_FROM",
			"WFC_SMTP_HOST", "WFC_SMTP_PORT", "WFC_SMTP_USER", "WFC_SMTP_PASSWORD", "WFC_SMTP_FROM",
			"ADMIN_EMAIL", "REGISTRATION_EMAIL",
			"JWT_SECRET",
		} {
			value := os.Getenv(env)
			if value == "" {
				logger.Debug("Omgevingsvariabele niet gevonden", "key", env)
			} else {
				// Verberg wachtwoorden in logs
				if strings.Contains(env, "PASSWORD") {
					logger.Debug("Omgevingsvariabele gevonden", "key", env, "value", "********")
				} else {
					logger.Debug("Omgevingsvariabele gevonden", "key", env, "value", value)
				}
			}
		}
	} else {
		logger.Info("Omgevingsvariabelen debug overgeslagen (alleen beschikbaar in DEBUG modus)")
	}

	// Setup ELK integratie als omgevingsvariabele is ingesteld
	elkEndpoint := os.Getenv("ELK_ENDPOINT")
	if elkEndpoint != "" {
		logger.SetupELK(logger.ELKConfig{
			Endpoint:      elkEndpoint,
			BatchSize:     100,
			FlushInterval: 5 * time.Second,
			AppName:       "dklemailservice",
			Environment:   os.Getenv("ENVIRONMENT"),
		})
		logger.Info("ELK logging enabled", "endpoint", elkEndpoint)
	}

	logger.Info("DKL Email Service wordt gestart", "version", handlers.Version)

	// Controleer omgevingsvariabelen
	if err := ValidateEnv(); err != nil {
		logger.Fatal("Configuratiefout", "error", err)
	}

	// Initialiseer database
	dbConfig := config.LoadDatabaseConfig()

	// Log database configuratie voor debugging
	logger.Info("Database configuratie geladen",
		"host", dbConfig.Host,
		"port", dbConfig.Port,
		"user", dbConfig.User,
		"dbname", dbConfig.DBName,
		"sslmode", dbConfig.SSLMode)

	// Test database verbinding direct
	connectionString := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbConfig.Host, dbConfig.Port, dbConfig.User, dbConfig.Password, dbConfig.DBName, dbConfig.SSLMode)
	logger.Info("Probeer directe database verbinding", "connection_string", connectionString)

	db, err := config.InitDatabase(dbConfig)
	if err != nil {
		logger.Fatal("Database initialisatie fout", "error", err)
	}

	// Initialiseer repository factory
	repoFactory := repository.NewRepository(db)

	// Voer database migraties uit
	migrationManager := database.NewMigrationManager(db, repoFactory.Migratie)
	if err := migrationManager.MigrateDatabase(); err != nil {
		logger.Fatal("Database migratie fout", "error", err)
	}

	// Seed database met initiële data
	if err := migrationManager.SeedDatabase(); err != nil {
		logger.Fatal("Database seeding fout", "error", err)
	}

	// Initialiseer service factory
	serviceFactory := services.NewServiceFactory(repoFactory)

	// Initialiseer steps service
	stepsService := services.NewStepsService(db, repoFactory.Aanmelding, repoFactory.RouteFund)

	// ✨ NIEUWE: Initialize StepsHub voor WebSocket real-time updates
	stepsHub := services.NewStepsHub(stepsService, serviceFactory.GamificationService)

	// ✨ NIEUWE: Link hub to service voor broadcasts
	stepsService.SetStepsHub(stepsHub)

	// ✨ NIEUWE: Start hub in background goroutine
	go stepsHub.Run()
	logger.Info("StepsHub started successfully - WebSocket support enabled")

	// Start Newsletter service indien geconfigureerd
	if serviceFactory.NewsletterService != nil {
		serviceFactory.NewsletterService.Start()
	}

	// Gebruik de GetRateLimiter methode in de ServiceFactory om direct het concrete
	// type terug te krijgen, zonder type assertion
	rateLimiter := serviceFactory.GetRateLimiter()

	// Stel rate limiter en Redis client in voor health checks
	handlers.SetRateLimiter(rateLimiter)
	handlers.SetRedisClient(serviceFactory.RedisClient)

	// Initialiseer handlers
	emailHandler := handlers.NewEmailHandler(
		serviceFactory.EmailService,
		serviceFactory.NotificationService,
		repoFactory.Aanmelding,
	)
	authHandler := handlers.NewAuthHandler(serviceFactory.AuthService, serviceFactory.PermissionService, rateLimiter)
	metricsHandler := handlers.NewMetricsHandler(serviceFactory.EmailMetrics, rateLimiter)

	// Initialiseer NotificationHandler
	notificationHandler := handlers.NewNotificationHandler(
		repoFactory.Notification,
		serviceFactory.NotificationService,
		serviceFactory.AuthService,
	)

	// Initialiseer nieuwe handlers voor contact en aanmelding beheer
	contactHandler := handlers.NewContactHandler(
		repoFactory.Contact,
		repoFactory.ContactAntwoord,
		serviceFactory.EmailService,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
		serviceFactory.NotificationService,
	)

	aanmeldingHandler := handlers.NewAanmeldingHandler(
		repoFactory.Aanmelding,
		repoFactory.AanmeldingAntwoord,
		serviceFactory.EmailService,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)

	// Initialiseer steps handler
	stepsHandler := handlers.NewStepsHandler(
		stepsService,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)

	// Initialiseer newsletter handler
	newsletterHandler := handlers.NewNewsletterHandler(
		repoFactory.Newsletter,
		serviceFactory.NewsletterSender,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)

	// Configureer en initialiseer de mail fetcher service
	mailFetcher := initializeMailFetcher(serviceFactory.EmailMetrics)
	mailHandler := handlers.NewMailHandler(mailFetcher, repoFactory.IncomingEmail, serviceFactory.AuthService, serviceFactory.PermissionService)

	// Maak een EmailAutoFetcher aan voor automatisch ophalen van emails
	emailAutoFetcher := services.NewEmailAutoFetcher(mailFetcher, repoFactory.IncomingEmail)

	// Sla de emailAutoFetcher op in de serviceFactory
	serviceFactory.EmailAutoFetcher = emailAutoFetcher

	// Start de automatische email fetcher als deze niet is uitgeschakeld
	if os.Getenv("DISABLE_AUTO_EMAIL_FETCH") != "true" {
		logger.Info("Automatisch ophalen van emails starten...")
		serviceFactory.EmailAutoFetcher.Start()
		logger.Info("Automatische email fetcher gestart")
	} else {
		logger.Info("Automatisch ophalen van emails is uitgeschakeld")
	}

	// Create Fiber app
	app := fiber.New(fiber.Config{
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			logger.Error("Request fout",
				"path", c.Path(),
				"method", c.Method(),
				"error", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Er is een fout opgetreden bij het verwerken van je verzoek",
			})
		},
	})

	// Configure CORS
	allowedOrigins := strings.Split(os.Getenv("ALLOWED_ORIGINS"), ",")
	if len(allowedOrigins) == 0 || (len(allowedOrigins) == 1 && allowedOrigins[0] == "") {
		allowedOrigins = []string{"https://www.dekoninklijkeloop.nl", "https://dekoninklijkeloop.nl", "https://admin.dekoninklijkeloop.nl", "http://localhost:3000", "http://localhost:5173"}
	}

	logger.Info("CORS geconfigureerd", "origins", allowedOrigins)

	app.Use(cors.New(cors.Config{
		AllowOrigins:     strings.Join(allowedOrigins, ","),
		AllowHeaders:     "Origin, Content-Type, Accept, Authorization, X-Test-Mode",
		AllowMethods:     "GET,POST,PUT,DELETE,OPTIONS",
		AllowCredentials: true,
		ExposeHeaders:    "Content-Length, Content-Type",
	}))

	// Voeg TestModeMiddleware toe als globale middleware
	app.Use(handlers.TestModeMiddleware())

	// Serve static files from public directory
	app.Static("/", "./public")

	// Specific route for favicon.ico
	app.Get("/favicon.ico", func(c *fiber.Ctx) error {
		// Get the current working directory
		workDir, err := os.Getwd()
		if err != nil {
			logger.Error("Kon werkdirectory niet bepalen", "error", err)
			return c.SendStatus(fiber.StatusInternalServerError)
		}

		faviconPath := filepath.Join(workDir, "public", "favicon.ico")
		if _, err := os.Stat(faviconPath); os.IsNotExist(err) {
			logger.Error("Favicon niet gevonden", "path", faviconPath, "error", err)
			return c.SendStatus(fiber.StatusNotFound)
		}
		c.Set("Content-Type", "image/x-icon")
		c.Set("Cache-Control", "public, max-age=31536000") // Cache voor 1 jaar
		return c.SendFile(faviconPath, false)
	})

	// Root route
	app.Get("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"service":     "DKL Email Service API",
			"version":     handlers.Version,
			"status":      "running",
			"environment": os.Getenv("ENVIRONMENT"),
			"timestamp":   time.Now(),
			"endpoints": []fiber.Map{
				{"path": "/api/health", "method": "GET", "description": "Service health status"},
				{"path": "/api/contact-email", "method": "POST", "description": "Send contact form email"},
				{"path": "/api/aanmelding-email", "method": "POST", "description": "Send registration form email"},
				{"path": "/api/metrics/email", "method": "GET", "description": "Email metrics (requires API key)"},
				{"path": "/api/metrics/rate-limits", "method": "GET", "description": "Rate limit metrics (requires API key)"},
				{"path": "/api/auth/login", "method": "POST", "description": "User login"},
				{"path": "/api/auth/logout", "method": "POST", "description": "User logout"},
				{"path": "/api/auth/profile", "method": "GET", "description": "Get user profile (requires auth)"},
				{"path": "/api/auth/reset-password", "method": "POST", "description": "Reset password (requires auth)"},
				{"path": "/api/contact", "method": "GET", "description": "List contact forms (requires admin auth)"},
				{"path": "/api/contact/:id", "method": "GET", "description": "Get contact form details (requires admin auth)"},
				{"path": "/api/contact/:id", "method": "PUT", "description": "Update contact form (requires admin auth)"},
				{"path": "/api/contact/:id", "method": "DELETE", "description": "Delete contact form (requires admin auth)"},
				{"path": "/api/contact/:id/antwoord", "method": "POST", "description": "Add reply to contact form (requires admin auth)"},
				{"path": "/api/contact/status/:status", "method": "GET", "description": "Filter contact forms by status (requires admin auth)"},
				{"path": "/api/aanmelding", "method": "GET", "description": "List registrations (requires admin auth)"},
				{"path": "/api/aanmelding/:id", "method": "GET", "description": "Get registration details (requires admin auth)"},
				{"path": "/api/aanmelding/:id", "method": "PUT", "description": "Update registration (requires admin auth)"},
				{"path": "/api/aanmelding/:id", "method": "DELETE", "description": "Delete registration (requires admin auth)"},
				{"path": "/api/aanmelding/:id/antwoord", "method": "POST", "description": "Add reply to registration (requires admin auth)"},
				{"path": "/api/aanmelding/rol/:rol", "method": "GET", "description": "Filter registrations by role (requires admin auth)"},
				{"path": "/api/aanmeldingen", "method": "GET", "description": "List registrations (alias, requires admin auth)"},
				{"path": "/api/aanmeldingen/:id", "method": "GET", "description": "Get registration details (alias, requires admin auth)"},
				{"path": "/api/aanmeldingen/:id", "method": "PUT", "description": "Update registration (alias, requires admin auth)"},
				{"path": "/api/aanmeldingen/:id", "method": "DELETE", "description": "Delete registration (alias, requires admin auth)"},
				{"path": "/api/aanmeldingen/:id/antwoord", "method": "POST", "description": "Add reply to registration (alias, requires admin auth)"},
				{"path": "/api/aanmeldingen/rol/:rol", "method": "GET", "description": "Filter registrations by role (alias, requires admin auth)"},
				{"path": "/api/wfc/order-email", "method": "POST", "description": "Send Whisky for Charity order emails (requires API key)"},
				{"path": "/api/images/upload", "method": "POST", "description": "Upload single image (requires auth)"},
				{"path": "/api/images/batch-upload", "method": "POST", "description": "Upload multiple images (requires auth)"},
				{"path": "/api/images/:public_id", "method": "GET", "description": "Get image metadata (requires auth)"},
				{"path": "/api/images/:public_id", "method": "DELETE", "description": "Delete image (requires auth)"},
				{"path": "/api/partners", "method": "GET", "description": "Get visible partners (public)"},
				{"path": "/api/partners/admin", "method": "GET", "description": "List all partners (requires admin auth)"},
				{"path": "/api/partners/:id", "method": "GET", "description": "Get partner by ID (requires admin auth)"},
				{"path": "/api/partners", "method": "POST", "description": "Create partner (requires admin auth)"},
				{"path": "/api/partners/:id", "method": "PUT", "description": "Update partner (requires admin auth)"},
				{"path": "/api/partners/:id", "method": "DELETE", "description": "Delete partner (requires admin auth)"},
				{"path": "/api/radio-recordings", "method": "GET", "description": "Get visible radio recordings (public)"},
				{"path": "/api/radio-recordings/admin", "method": "GET", "description": "List all radio recordings (requires admin auth)"},
				{"path": "/api/radio-recordings/:id", "method": "GET", "description": "Get radio recording by ID (requires admin auth)"},
				{"path": "/api/radio-recordings", "method": "POST", "description": "Create radio recording (requires admin auth)"},
				{"path": "/api/radio-recordings/:id", "method": "PUT", "description": "Update radio recording (requires admin auth)"},
				{"path": "/api/radio-recordings/:id", "method": "DELETE", "description": "Delete radio recording (requires admin auth)"},
				{"path": "/api/photos", "method": "GET", "description": "Get visible photos (public). Supports filtering: ?year=2024&title=search&description=search&cloudinary_folder=folder"},
				{"path": "/api/photos/admin", "method": "GET", "description": "List all photos (requires admin auth)"},
				{"path": "/api/photos/:id", "method": "GET", "description": "Get photo by ID (requires admin auth)"},
				{"path": "/api/photos", "method": "POST", "description": "Create photo (requires admin auth)"},
				{"path": "/api/photos/:id", "method": "PUT", "description": "Update photo (requires admin auth)"},
				{"path": "/api/photos/:id", "method": "DELETE", "description": "Delete photo (requires admin auth)"},
				{"path": "/api/albums", "method": "GET", "description": "Get visible albums (public). Use ?include_covers=true for cover photos"},
				{"path": "/api/albums/:id/photos", "method": "GET", "description": "Get photos for album (public)"},
				{"path": "/api/albums/admin", "method": "GET", "description": "List all albums (requires admin auth)"},
				{"path": "/api/albums/:id", "method": "GET", "description": "Get album by ID (requires admin auth)"},
				{"path": "/api/albums", "method": "POST", "description": "Create album (requires admin auth)"},
				{"path": "/api/albums/:id", "method": "PUT", "description": "Update album (requires admin auth)"},
				{"path": "/api/albums/:id", "method": "DELETE", "description": "Delete album (requires admin auth)"},
				{"path": "/api/videos", "method": "GET", "description": "Get visible videos (public)"},
				{"path": "/api/videos/admin", "method": "GET", "description": "List all videos (requires admin auth)"},
				{"path": "/api/videos/:id", "method": "GET", "description": "Get video by ID (requires admin auth)"},
				{"path": "/api/videos", "method": "POST", "description": "Create video (requires admin auth)"},
				{"path": "/api/videos/:id", "method": "PUT", "description": "Update video (requires admin auth)"},
				{"path": "/api/videos/:id", "method": "DELETE", "description": "Delete video (requires admin auth)"},
				{"path": "/api/sponsors", "method": "GET", "description": "Get visible sponsors (public)"},
				{"path": "/api/sponsors/admin", "method": "GET", "description": "List all sponsors (requires admin auth)"},
				{"path": "/api/sponsors/:id", "method": "GET", "description": "Get sponsor by ID (requires admin auth)"},
				{"path": "/api/sponsors", "method": "POST", "description": "Create sponsor (requires admin auth)"},
				{"path": "/api/sponsors/:id", "method": "PUT", "description": "Update sponsor (requires admin auth)"},
				{"path": "/api/sponsors/:id", "method": "DELETE", "description": "Delete sponsor (requires admin auth)"},
				{"path": "/api/program-schedule", "method": "GET", "description": "Get visible program schedule (public)"},
				{"path": "/api/program-schedule/admin", "method": "GET", "description": "List all program schedule (requires admin auth)"},
				{"path": "/api/program-schedule/:id", "method": "GET", "description": "Get program schedule by ID (requires admin auth)"},
				{"path": "/api/program-schedule", "method": "POST", "description": "Create program schedule (requires admin auth)"},
				{"path": "/api/program-schedule/:id", "method": "PUT", "description": "Update program schedule (requires admin auth)"},
				{"path": "/api/program-schedule/:id", "method": "DELETE", "description": "Delete program schedule (requires admin auth)"},
				{"path": "/api/social-embeds", "method": "GET", "description": "Get visible social embeds (public)"},
				{"path": "/api/social-embeds/admin", "method": "GET", "description": "List all social embeds (requires admin auth)"},
				{"path": "/api/social-embeds/:id", "method": "GET", "description": "Get social embed by ID (requires admin auth)"},
				{"path": "/api/social-embeds", "method": "POST", "description": "Create social embed (requires admin auth)"},
				{"path": "/api/social-embeds/:id", "method": "PUT", "description": "Update social embed (requires admin auth)"},
				{"path": "/api/social-embeds/:id", "method": "DELETE", "description": "Delete social embed (requires admin auth)"},
				{"path": "/api/social-links", "method": "GET", "description": "Get visible social links (public)"},
				{"path": "/api/social-links/admin", "method": "GET", "description": "List all social links (requires admin auth)"},
				{"path": "/api/social-links/:id", "method": "GET", "description": "Get social link by ID (requires admin auth)"},
				{"path": "/api/social-links", "method": "POST", "description": "Create social link (requires admin auth)"},
				{"path": "/api/social-links/:id", "method": "PUT", "description": "Update social link (requires admin auth)"},
				{"path": "/api/social-links/:id", "method": "DELETE", "description": "Delete social link (requires admin auth)"},
				{"path": "/api/under-construction/active", "method": "GET", "description": "Get active under construction (public)"},
				{"path": "/api/under-construction/admin", "method": "GET", "description": "List all under construction (requires admin auth)"},
				{"path": "/api/under-construction/:id", "method": "GET", "description": "Get under construction by ID (requires admin auth)"},
				{"path": "/api/under-construction", "method": "POST", "description": "Create under construction (requires admin auth)"},
				{"path": "/api/under-construction/:id", "method": "PUT", "description": "Update under construction (requires admin auth)"},
				{"path": "/api/under-construction/:id", "method": "DELETE", "description": "Delete under construction (requires admin auth)"},
				{"path": "/api/title_section_content", "method": "GET", "description": "Get title section content (public)"},
				{"path": "/api/title_section_content/admin", "method": "GET", "description": "Get title section content for admin (requires admin auth)"},
				{"path": "/api/title_section_content", "method": "POST", "description": "Create title section content (requires admin auth)"},
				{"path": "/api/title_section_content", "method": "PUT", "description": "Update title section content (requires admin auth)"},
				{"path": "/api/title_section_content/:id", "method": "DELETE", "description": "Delete title section content (requires admin auth)"},
				{"path": "/api/steps/:id", "method": "POST", "description": "Update steps for participant (requires steps write permission)"},
				{"path": "/api/participant/:id/dashboard", "method": "GET", "description": "Get participant dashboard (requires steps read permission)"},
				{"path": "/api/total-steps", "method": "GET", "description": "Get total steps for year (requires steps read permission)"},
				{"path": "/api/funds-distribution", "method": "GET", "description": "Get funds distribution (requires steps read permission)"},
				{"path": "/api/events", "method": "GET", "description": "List events (public)"},
				{"path": "/api/events/active", "method": "GET", "description": "Get active event (public)"},
				{"path": "/api/events/:id", "method": "GET", "description": "Get event details (public)"},
				{"path": "/api/events", "method": "POST", "description": "Create event (requires events write permission)"},
				{"path": "/api/events/:id", "method": "PUT", "description": "Update event (requires events write permission)"},
				{"path": "/api/events/:id", "method": "DELETE", "description": "Delete event (requires events write permission)"},
				{"path": "/api/events/:id/participants", "method": "GET", "description": "Get event participants (requires events read permission)"},
				{"path": "/metrics", "method": "GET", "description": "Prometheus metrics"},
			},
		})
	})

	// API routes group
	api := app.Group("/api")

	// Health check endpoint
	api.Get("/health", handlers.HealthHandler)

	// Email routes
	api.Post("/contact-email", emailHandler.HandleContactEmail)
	api.Post("/aanmelding-email", emailHandler.HandleAanmeldingEmail)

	// Auth routes
	auth := api.Group("/auth")
	auth.Post("/login", handlers.RateLimitMiddleware(rateLimiter, "login"), authHandler.HandleLogin)
	auth.Post("/logout", authHandler.HandleLogout)
	auth.Post("/refresh", authHandler.HandleRefreshToken)

	// Beveiligde auth routes (vereisen authenticatie)
	authProtected := auth.Group("/", handlers.AuthMiddleware(serviceFactory.AuthService))
	authProtected.Get("/profile", authHandler.HandleGetProfile)
	authProtected.Post("/reset-password", authHandler.HandleResetPassword)

	// Metrics endpoints direct onder /api/metrics/... (vereisen API key)
	api.Get("/metrics/email", metricsHandler.HandleGetEmailMetrics)
	api.Get("/metrics/rate-limits", metricsHandler.HandleGetRateLimits)

	// Registreer routes voor contact en aanmelding beheer
	contactHandler.RegisterRoutes(app)
	aanmeldingHandler.RegisterRoutes(app)

	// Registreer routes voor stappen beheer
	stepsHandler.RegisterRoutes(app)

	// ✨ NIEUWE: Initialiseer en registreer WebSocket handler voor steps
	stepsWsHandler := handlers.NewStepsWebSocketHandler(stepsHub, serviceFactory.AuthService)
	stepsWsHandler.RegisterRoutes(app)
	logger.Info("WebSocket routes registered - /ws/steps endpoint active")

	// ✨ NIEUWE: WebSocket stats endpoint (admin only)
	app.Get("/api/ws/stats",
		handlers.AuthMiddleware(serviceFactory.AuthService),
		handlers.PermissionMiddleware(serviceFactory.PermissionService, "admin", "read"),
		stepsWsHandler.GetStats,
	)

	// Registreer routes voor newsletter beheer
	newsletterHandler.RegisterRoutes(app)

	// Registreer routes voor notificaties
	notificationHandler.RegisterRoutes(app)

	// Registreer de mailHandler in de main functie na repo en authService
	mailHandler.RegisterRoutes(app)

	// Registreer de WFC routes voor order emails
	// Deze routes gebruiken aparte API key authenticatie en worden niet in telegram gelogd
	handlers.RegisterWFCOrderRoutes(app, serviceFactory.EmailService)

	// Registreer telegram bot handler indien ingeschakeld
	if serviceFactory.TelegramBotService != nil {
		// Registreer Telegram API endpoints direct in Fiber
		app.Get("/api/v1/telegrambot/config", func(c *fiber.Ctx) error {
			// JWT authenticatie controleren
			authHeader := c.Get("Authorization")
			if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
				return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
					"error": "Unauthorized",
				})
			}

			// Check bestaande service
			if serviceFactory.TelegramBotService == nil {
				return c.Status(fiber.StatusOK).JSON(fiber.Map{
					"enabled":  false,
					"message":  "Telegram bot service is niet geactiveerd",
					"chatId":   "",
					"commands": []string{},
				})
			}

			// Gegevens ophalen
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"enabled": true,
				"message": "Telegram bot service is actief",
				"chatId":  serviceFactory.TelegramBotService.GetChatID(),
			})
		})

		app.Post("/api/v1/telegrambot/send", func(c *fiber.Ctx) error {
			// JWT authenticatie controleren
			authHeader := c.Get("Authorization")
			if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
				return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
					"error": "Unauthorized",
				})
			}

			// Check bestaande service
			if serviceFactory.TelegramBotService == nil {
				return c.Status(fiber.StatusOK).JSON(fiber.Map{
					"success": false,
					"message": "Telegram bot service is niet geactiveerd",
				})
			}

			// Parse request body
			var req struct {
				Message string `json:"message"`
			}

			if err := c.BodyParser(&req); err != nil {
				return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
					"success": false,
					"message": "Ongeldige request",
				})
			}

			// Bericht versturen
			err := serviceFactory.TelegramBotService.SendMessage(req.Message)
			if err != nil {
				logger.Error("Fout bij verzenden Telegram bericht", "error", err)
				return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
					"success": false,
					"message": "Fout bij verzenden bericht: " + err.Error(),
				})
			}

			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"success": true,
				"message": "Bericht succesvol verzonden",
			})
		})

		app.Get("/api/v1/telegrambot/commands", func(c *fiber.Ctx) error {
			// JWT authenticatie controleren
			authHeader := c.Get("Authorization")
			if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
				return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
					"error": "Unauthorized",
				})
			}

			// Check bestaande service
			if serviceFactory.TelegramBotService == nil {
				return c.Status(fiber.StatusOK).JSON(fiber.Map{
					"success":  false,
					"message":  "Telegram bot service is niet geactiveerd",
					"commands": []interface{}{},
				})
			}

			// Gegevens ophalen
			commands := serviceFactory.TelegramBotService.GetCommands()

			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"success":  true,
				"message":  "Commando's succesvol opgehaald",
				"commands": commands,
			})
		})

		logger.Info("Telegram bot routes geregistreerd")
	}

	// Voeg Prometheus metrics endpoint toe aan standaard HTTP server
	app.Get("/metrics", func(c *fiber.Ctx) error {
		// Gebruik een simpele proxy naar de standaard Prometheus HTTP handler
		registry := prometheus.DefaultRegisterer.(*prometheus.Registry)
		handler := promhttp.HandlerFor(registry, promhttp.HandlerOpts{})

		// Maak een HTTP test recorder om de output vast te leggen
		recorder := httptest.NewRecorder()
		request := httptest.NewRequest("GET", "/metrics", nil)

		// Voer de request uit
		handler.ServeHTTP(recorder, request)

		// Kopieer headers naar Fiber response
		for k, v := range recorder.Header() {
			for _, val := range v {
				c.Set(k, val)
			}
		}

		// Stuur de body terug met de juiste status code
		return c.Status(recorder.Code).Send(recorder.Body.Bytes())
	})

	// Initialiseer de nieuwe admin mail handler met email repository voor reprocessing
	adminMailHandler := handlers.NewAdminMailHandler(serviceFactory.EmailService, serviceFactory.AuthService, serviceFactory.PermissionService, repoFactory.IncomingEmail)

	// Registreer de admin mail routes
	adminMailHandler.RegisterRoutes(app)

	// Initialiseer chat handler
	chatHandler := handlers.NewChatHandler(serviceFactory.ChatService, serviceFactory.AuthService, serviceFactory.PermissionService, serviceFactory.ImageService, serviceFactory.Hub)
	chatHandler.RegisterRoutes(app)

	// Set WebSocket channel callback
	chatHandler.SetChannelHubCallback()

	// Initialiseer permission en role handlers
	permissionHandler := handlers.NewPermissionHandler(
		repoFactory.Permission,
		repoFactory.RBACRole,
		repoFactory.RolePermission,
		repoFactory.UserRole,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	permissionHandler.RegisterRoutes(app)

	// Initialiseer user handler
	userHandler := handlers.NewUserHandler(serviceFactory.AuthService, serviceFactory.PermissionService, repoFactory.UserRole, repoFactory.RBACRole)
	userHandler.RegisterRoutes(app)

	// Initialiseer image handler
	imageHandler := handlers.NewImageHandler(serviceFactory.ImageService, serviceFactory.AuthService)
	imageHandler.RegisterRoutes(app)

	// Initialiseer partner handler
	partnerHandler := handlers.NewPartnerHandler(
		repoFactory.Partner,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	partnerHandler.RegisterRoutes(app)

	// Initialiseer radio recording handler
	radioRecordingHandler := handlers.NewRadioRecordingHandler(
		repoFactory.RadioRecording,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	radioRecordingHandler.RegisterRoutes(app)

	// Initialiseer photo handler
	photoHandler := handlers.NewPhotoHandler(
		repoFactory.Photo,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	photoHandler.RegisterRoutes(app)

	// Initialiseer album handler
	albumHandler := handlers.NewAlbumHandler(
		repoFactory.Album,
		repoFactory.Photo,
		repoFactory.AlbumPhoto,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	albumHandler.RegisterRoutes(app)

	// Initialiseer video handler
	videoHandler := handlers.NewVideoHandler(
		repoFactory.Video,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	videoHandler.RegisterRoutes(app)

	// Initialiseer sponsor handler
	sponsorHandler := handlers.NewSponsorHandler(
		repoFactory.Sponsor,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
		serviceFactory.ImageService,
	)
	sponsorHandler.RegisterRoutes(app)

	// Initialiseer program schedule handler
	programScheduleHandler := handlers.NewProgramScheduleHandler(
		repoFactory.ProgramSchedule,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	programScheduleHandler.RegisterRoutes(app)

	// Initialiseer social embed handler
	socialEmbedHandler := handlers.NewSocialEmbedHandler(
		repoFactory.SocialEmbed,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	socialEmbedHandler.RegisterRoutes(app)

	// Initialiseer social link handler
	socialLinkHandler := handlers.NewSocialLinkHandler(
		repoFactory.SocialLink,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	socialLinkHandler.RegisterRoutes(app)

	// Initialiseer under construction handler
	underConstructionHandler := handlers.NewUnderConstructionHandler(
		repoFactory.UnderConstruction,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	underConstructionHandler.RegisterRoutes(app)

	// Initialiseer title section handler
	titleSectionHandler := handlers.NewTitleSectionHandler(
		repoFactory.TitleSection,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	titleSectionHandler.RegisterRoutes(app)

	// Initialiseer gamification handler
	gamificationHandler := handlers.NewGamificationHandler(
		serviceFactory.GamificationService,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	gamificationHandler.RegisterRoutes(app)

	// Initialiseer event handler
	eventHandler := handlers.NewEventHandler(
		repoFactory.Event,
		serviceFactory.AuthService,
		serviceFactory.PermissionService,
	)
	eventHandler.RegisterRoutes(app)

	// Initialiseer notulen handler
	notulenHandler := handlers.NewNotulenHandler(serviceFactory.NotulenService, serviceFactory.AuthService, serviceFactory.PermissionService)
	notulenHandler.RegisterRoutes(app)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080" // Default to 8080 for web traffic
	}

	// Start server in een goroutine met Fiber's eigen methoden
	go func() {
		logger.Info("Server gestart", "port", port)
		if err := app.Listen(":" + port); err != nil {
			logger.Fatal("Server fout", "error", err)
		}
	}()

	// Wacht op interrupt signaal (CTRL+C)
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, os.Interrupt, syscall.SIGTERM)
	<-stop
	logger.Info("Server wordt afgesloten...")

	// Graceful shutdown
	if serviceFactory.EmailBatcher != nil {
		serviceFactory.EmailBatcher.Shutdown()
	}

	// Stop de email auto fetcher
	if serviceFactory.EmailAutoFetcher != nil && serviceFactory.EmailAutoFetcher.IsRunning() {
		logger.Info("Email auto fetcher stoppen...")
		serviceFactory.EmailAutoFetcher.Stop()
		logger.Info("Email auto fetcher gestopt")
	}

	// Stop de Newsletter service
	if serviceFactory.NewsletterService != nil {
		serviceFactory.NewsletterService.Stop()
	}

	// Sluit rate limiter af
	if rateLimiter != nil {
		rateLimiter.Shutdown()
	}

	// Log laatste metrics
	serviceFactory.EmailMetrics.LogMetrics()

	// Sluit alle log writers
	logger.CloseWriters()

	// Graceful shutdown met Fiber
	if err := app.Shutdown(); err != nil {
		logger.Fatal("Server shutdown fout", "error", err)
	}

	logger.Info("Server succesvol afgesloten")
}

// Configureer en initialiseer de mail fetcher service
func initializeMailFetcher(metrics *services.EmailMetrics) *services.MailFetcher {
	mailFetcher := services.NewMailFetcher(metrics)

	// Get email account credentials from environment variables
	infoEmail := os.Getenv("INFO_EMAIL")
	infoPassword := os.Getenv("INFO_EMAIL_PASSWORD")
	inschrijvingEmail := os.Getenv("INSCHRIJVING_EMAIL")
	inschrijvingPassword := os.Getenv("INSCHRIJVING_EMAIL_PASSWORD")
	imapServer := os.Getenv("IMAP_SERVER")
	imapPort := os.Getenv("IMAP_PORT")

	// Default values for server and port if not set
	if imapServer == "" {
		imapServer = "mail.hostnet.nl"
		logger.Warn("IMAP_SERVER not set, using default", "server", imapServer)
	}

	port := 993 // Default IMAP SSL port
	if imapPort != "" {
		if p, err := strconv.Atoi(imapPort); err == nil {
			port = p
		} else {
			logger.Warn("Invalid IMAP_PORT, using default", "port", port, "error", err)
		}
	}

	// Add the accounts if credentials are provided
	if infoEmail != "" && infoPassword != "" {
		mailFetcher.AddAccount(
			infoEmail,
			infoPassword,
			imapServer,
			port,
			"info",
		)
		logger.Info("Added info email account", "email", infoEmail)
	} else {
		logger.Warn("Info email credentials not set, skipping account setup")
	}

	if inschrijvingEmail != "" && inschrijvingPassword != "" {
		mailFetcher.AddAccount(
			inschrijvingEmail,
			inschrijvingPassword,
			imapServer,
			port,
			"inschrijving",
		)
		logger.Info("Added inschrijving email account", "email", inschrijvingEmail)
	} else {
		logger.Warn("Inschrijving email credentials not set, skipping account setup")
	}

	return mailFetcher
}
