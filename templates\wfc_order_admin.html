<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>New Order Received - Whisky for Charity</title>
    <style type="text/css">
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f5f5f5;
            padding: 15px;
            text-align: center;
            font-size: 12px;
            color: #777;
        }
        h1 {
            margin: 0;
        }
        h2 {
            color: #2c3e50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .total {
            font-weight: bold;
            text-align: right;
        }
        .button {
            display: inline-block;
            background-color: #2c3e50;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 15px;
        }
        .info-box {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info-box h4 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            color: #2c3e50;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .highlight {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Whisky for Charity - Admin</h1>
    </div>
    <div class="content">
        <h2>New Order Received</h2>
        <p>A new order has been placed on the Whisky for Charity website.</p>
        
        <div class="info-grid">
            <div class="info-box">
                <h4>Order Information</h4>
                <p><strong>Order ID:</strong> {{.Order.ID}}</p>
                <p><strong>Date:</strong> {{.Order.CreatedAt.Format "January 2, 2006 15:04"}}</p>
                <p><strong>Total Amount:</strong> <span class="highlight">€{{printf "%.2f" .Order.TotalAmount}}</span></p>
                <p><strong>Status:</strong> {{.Order.Status}}</p>
                {{if .Order.PaymentReference}}
                <p><strong>Payment Reference:</strong> {{.Order.PaymentReference}}</p>
                {{end}}
            </div>
            
            <div class="info-box">
                <h4>Customer Information</h4>
                <p><strong>Name:</strong> {{.Order.CustomerName}}</p>
                <p><strong>Email:</strong> <a href="mailto:{{.Order.CustomerEmail}}">{{.Order.CustomerEmail}}</a></p>
                {{if .Order.CustomerAddress}}
                <p><strong>Address:</strong> {{.Order.CustomerAddress}}</p>
                {{end}}
                {{if or .Order.CustomerCity .Order.CustomerPostal}}
                <p>
                    <strong>City/Postal:</strong> 
                    {{if .Order.CustomerPostal}}{{.Order.CustomerPostal}}{{end}}
                    {{if .Order.CustomerCity}}{{.Order.CustomerCity}}{{end}}
                </p>
                {{end}}
                {{if .Order.CustomerCountry}}
                <p><strong>Country:</strong> {{.Order.CustomerCountry}}</p>
                {{end}}
            </div>
        </div>
        
        <div class="info-box">
            <h4>Order Details</h4>
            <table>
                <thead>
                    <tr>
                        <th>Item</th>
                        <th>Product ID</th>
                        <th>Quantity</th>
                        <th>Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {{range .Order.Items}}
                    <tr>
                        <td>{{.ProductName}}</td>
                        <td>{{.ProductID}}</td>
                        <td>{{.Quantity}}</td>
                        <td>€{{printf "%.2f" .Price}}</td>
                        <td>€{{printf "%.2f" (multiply .Price .Quantity)}}</td>
                    </tr>
                    {{end}}
                    <tr>
                        <td colspan="4" class="total">Total Amount:</td>
                        <td>€{{printf "%.2f" .Order.TotalAmount}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="info-box">
            <h4>Shipping Information</h4>
            <p>
                {{.Order.CustomerName}}<br>
                {{if .Order.CustomerAddress}}{{.Order.CustomerAddress}}<br>{{end}}
                {{if .Order.CustomerPostal}}{{.Order.CustomerPostal}}{{end}} {{if .Order.CustomerCity}}{{.Order.CustomerCity}}{{end}}<br>
                {{if .Order.CustomerCountry}}{{.Order.CustomerCountry}}{{end}}
            </p>
        </div>
        
        {{if .SiteURL}}
        <p>
            <a href="{{.SiteURL}}/admin/orders/{{.Order.ID}}" class="button">View Order in Admin Panel</a>
        </p>
        {{end}}
    </div>
    <div class="footer">
        <p>Whisky for Charity &copy; {{currentYear}} - All Rights Reserved.</p>
        <p>This is an automated notification. Please do not reply to this email.</p>
    </div>
</body>
</html> 