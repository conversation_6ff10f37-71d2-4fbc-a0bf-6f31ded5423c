package models

import (
	"time"

	"github.com/google/uuid"
)

// Notulen represents a meeting minutes document
type Notulen struct {
	ID               uuid.UUID    `json:"id" db:"id"`
	Titel            string       `json:"titel" db:"titel"`
	VergaderingDatum time.Time    `json:"vergadering_datum" db:"vergadering_datum"`
	Locatie          string       `json:"locatie,omitempty" db:"locatie"`
	Voorzitter       string       `json:"voorzitter,omitempty" db:"voorzitter"`
	Notulist         string       `json:"notulist,omitempty" db:"notulist"`
	Aanwezigen       []string     `json:"aanwezigen,omitempty" db:"aanwezigen"`
	Afwezigen        []string     `json:"afwezigen,omitempty" db:"afwezigen"`
	AgendaItems      []AgendaItem `json:"agenda_items,omitempty" db:"agenda_items"`
	Besluiten        []Besluit    `json:"besluiten,omitempty" db:"besluiten"`
	Actiepunten      []Actiepunt  `json:"actiepunten,omitempty" db:"actiepunten"`
	Notities         string       `json:"notities,omitempty" db:"notities"`
	Status           string       `json:"status" db:"status"`
	Versie           int          `json:"versie" db:"versie"`
	CreatedBy        uuid.UUID    `json:"created_by" db:"created_by"`
	CreatedAt        time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time    `json:"updated_at" db:"updated_at"`
	FinalizedAt      *time.Time   `json:"finalized_at,omitempty" db:"finalized_at"`
	FinalizedBy      *uuid.UUID   `json:"finalized_by,omitempty" db:"finalized_by"`
}

// AgendaItem represents an item on the meeting agenda
type AgendaItem struct {
	Titel        string `json:"titel"`
	Beschrijving string `json:"beschrijving,omitempty"`
	Spreker      string `json:"spreker,omitempty"`
	Tijdslot     string `json:"tijdslot,omitempty"`
}

// Besluit represents a decision made during the meeting
type Besluit struct {
	Beschrijving      string     `json:"beschrijving"`
	Verantwoordelijke string     `json:"verantwoordelijke,omitempty"`
	Deadline          *time.Time `json:"deadline,omitempty"`
}

// Actiepunt represents an action item from the meeting
type Actiepunt struct {
	Beschrijving      string     `json:"beschrijving"`
	Verantwoordelijke string     `json:"verantwoordelijke"`
	Deadline          *time.Time `json:"deadline,omitempty"`
	Status            string     `json:"status"` // pending, in_progress, completed
}

// NotulenVersie represents a version snapshot of notulen
type NotulenVersie struct {
	ID               uuid.UUID    `json:"id" db:"id"`
	NotulenID        uuid.UUID    `json:"notulen_id" db:"notulen_id"`
	Versie           int          `json:"versie" db:"versie"`
	Titel            string       `json:"titel" db:"titel"`
	VergaderingDatum time.Time    `json:"vergadering_datum" db:"vergadering_datum"`
	Locatie          string       `json:"locatie,omitempty" db:"locatie"`
	Voorzitter       string       `json:"voorzitter,omitempty" db:"voorzitter"`
	Notulist         string       `json:"notulist,omitempty" db:"notulist"`
	Aanwezigen       []string     `json:"aanwezigen,omitempty" db:"aanwezigen"`
	Afwezigen        []string     `json:"afwezigen,omitempty" db:"afwezigen"`
	AgendaItems      []AgendaItem `json:"agenda_items,omitempty" db:"agenda_items"`
	Besluiten        []Besluit    `json:"besluiten,omitempty" db:"besluiten"`
	Actiepunten      []Actiepunt  `json:"actiepunten,omitempty" db:"actiepunten"`
	Notities         string       `json:"notities,omitempty" db:"notities"`
	Status           string       `json:"status" db:"status"`
	GewijzigdDoor    uuid.UUID    `json:"gewijzigd_door" db:"gewijzigd_door"`
	GewijzigdOp      time.Time    `json:"gewijzigd_op" db:"gewijzigd_op"`
	WijzigingReden   string       `json:"wijziging_reden,omitempty" db:"wijziging_reden"`
}

// NotulenCreateRequest represents the request to create new notulen
type NotulenCreateRequest struct {
	Titel            string       `json:"titel" validate:"required,min=3,max=255"`
	VergaderingDatum string       `json:"vergadering_datum" validate:"required"` // ISO date string
	Locatie          string       `json:"locatie,omitempty"`
	Voorzitter       string       `json:"voorzitter,omitempty"`
	Notulist         string       `json:"notulist,omitempty"`
	Aanwezigen       []string     `json:"aanwezigen,omitempty"`
	Afwezigen        []string     `json:"afwezigen,omitempty"`
	AgendaItems      []AgendaItem `json:"agenda_items,omitempty"`
	Besluiten        []Besluit    `json:"besluiten,omitempty"`
	Actiepunten      []Actiepunt  `json:"actiepunten,omitempty"`
	Notities         string       `json:"notities,omitempty"`
}

// NotulenUpdateRequest represents the request to update existing notulen
type NotulenUpdateRequest struct {
	Titel       string       `json:"titel,omitempty" validate:"omitempty,min=3,max=255"`
	Locatie     string       `json:"locatie,omitempty"`
	Voorzitter  string       `json:"voorzitter,omitempty"`
	Notulist    string       `json:"notulist,omitempty"`
	Aanwezigen  []string     `json:"aanwezigen,omitempty"`
	Afwezigen   []string     `json:"afwezigen,omitempty"`
	AgendaItems []AgendaItem `json:"agenda_items,omitempty"`
	Besluiten   []Besluit    `json:"besluiten,omitempty"`
	Actiepunten []Actiepunt  `json:"actiepunten,omitempty"`
	Notities    string       `json:"notities,omitempty"`
}

// NotulenListResponse represents the response for listing notulen
type NotulenListResponse struct {
	Notulen []Notulen `json:"notulen"`
	Total   int       `json:"total"`
	Limit   int       `json:"limit"`
	Offset  int       `json:"offset"`
}

// NotulenSearchFilters represents search and filter options
type NotulenSearchFilters struct {
	Query     string     `json:"query,omitempty"`      // Full-text search query
	DateFrom  *time.Time `json:"date_from,omitempty"`  // Start date filter
	DateTo    *time.Time `json:"date_to,omitempty"`    // End date filter
	Status    string     `json:"status,omitempty"`     // Status filter
	CreatedBy *uuid.UUID `json:"created_by,omitempty"` // Creator filter
	Limit     int        `json:"limit,omitempty"`      // Pagination limit
	Offset    int        `json:"offset,omitempty"`     // Pagination offset
}

// NotulenFinalizeRequest represents the request to finalize notulen
type NotulenFinalizeRequest struct {
	WijzigingReden string `json:"wijziging_reden,omitempty"`
}
