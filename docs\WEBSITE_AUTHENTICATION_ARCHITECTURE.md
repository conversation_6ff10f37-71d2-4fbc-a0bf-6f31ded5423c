# 🔐 Website Authentication Architecture - Complete Technical Overview

> **Version:** 1.0  
> **Status:** Production Ready  
> **Last Updated:** 2025-11-01  
> **Backend:** V1.48.0+  
> **For:** DKL Email Service & Admin Panel

Complete technische architectuurdocumentatie van het authenticatie- en autorisatiesysteem voor de website.

---

## 📋 Inhoudsopgave

1. [Executive Summary](#executive-summary)
2. [System Architecture](#system-architecture)
3. [Authentication Flow](#authentication-flow)
4. [Authorization (RBAC)](#authorization-rbac)
5. [Components Deep Dive](#components-deep-dive)
6. [Security Layers](#security-layers)
7. [API Endpoints](#api-endpoints)
8. [Database Schema](#database-schema)
9. [Caching Strategy](#caching-strategy)
10. [Route Protection Patterns](#route-protection-patterns)
11. [Error Handling](#error-handling)
12. [Performance & Monitoring](#performance--monitoring)
13. [Deployment Checklist](#deployment-checklist)
14. [Troubleshooting](#troubleshooting)
15. [Best Practices](#best-practices)

---

## 🎯 Executive Summary

Het DKL Email Service implementeert een enterprise-grade authenticatie- en autorisatiesysteem met:

### Kernfunctionaliteit
- ✅ **JWT Authentication** - 20 minuten access tokens met HS256 signing
- ✅ **Refresh Tokens** - 7 dagen levensduur met automatische rotatie
- ✅ **RBAC Authorization** - 19 resources, 58 granulaire permissions
- ✅ **Redis Caching** - 5 minuten cache, 97% hit rate
- ✅ **Multi-Layer Security** - 4 beveiligingslagen (UI, Route, Component, API)
- ✅ **Rate Limiting** - Bescherming tegen brute force aanvallen
- ✅ **Cookie Support** - HTTPOnly, Secure, SameSite cookies

### Technische Stack
- **Backend:** Go (Fiber framework)
- **Database:** PostgreSQL met GORM ORM
- **Cache:** Redis (permission caching)
- **Tokens:** JWT (golang-jwt/jwt/v5)
- **Password Hashing:** bcrypt
- **Frontend:** React met TypeScript

---

## 🏗️ System Architecture

### High-Level Overview

```
┌─────────────────────────── FRONTEND ─────────────────────────────┐
│                                                                    │
│  ┌─────────────┐    ┌──────────────┐    ┌──────────────────┐   │
│  │ Login Form  │───→│ AuthProvider │───→│ Token Management │   │
│  └─────────────┘    └──────────────┘    └──────────────────┘   │
│         │                    │                      │             │
│         │                    ↓                      ↓             │
│         │           ┌─────────────────┐   ┌────────────────┐    │
│         │           │ usePermissions  │   │ Protected      │    │
│         │           │ Hook            │   │ Routes         │    │
│         │           └─────────────────┘   └────────────────┘    │
│         │                    │                      │             │
└─────────┼────────────────────┼──────────────────────┼────────────┘
          │                    │                      │
          │ HTTP POST          │ HTTP GET             │ HTTP with
          │ Bearer Token       │ Bearer Token         │ Bearer Token
          ↓                    ↓                      ↓
┌─────────────────────────── BACKEND ──────────────────────────────┐
│                                                                    │
│  ┌──────────────┐    ┌──────────────┐    ┌──────────────────┐   │
│  │ Auth Handler │───→│ Auth Service │───→│ JWT Generation   │   │
│  └──────────────┘    └──────────────┘    └──────────────────┘   │
│         │                    │                      │             │
│         ↓                    ↓                      ↓             │
│  ┌──────────────┐    ┌──────────────┐    ┌──────────────────┐   │
│  │ Auth         │    │ Permission   │    │ Refresh Token    │   │
│  │ Middleware   │    │ Middleware   │    │ Rotation         │   │
│  └──────────────┘    └──────────────┘    └──────────────────┘   │
│         │                    │                      │             │
│         ↓                    ↓                      ↓             │
│  ┌──────────────────────────────────────────────────────────┐   │
│  │              PostgreSQL Database                          │   │
│  │  • gebruikers (users)                                     │   │
│  │  • roles (RBAC roles)                                     │   │
│  │  • permissions (granular permissions)                     │   │
│  │  • user_roles (many-to-many)                              │   │
│  │  • role_permissions (many-to-many)                        │   │
│  │  • refresh_tokens (token storage)                         │   │
│  └──────────────────────────────────────────────────────────┘   │
│                              ↕                                     │
│  ┌──────────────────────────────────────────────────────────┐   │
│  │              Redis Cache (5 min TTL)                      │   │
│  │  Key Format: perm:{userID}:{resource}:{action}           │   │
│  │  Hit Rate: ~97%                                           │   │
│  └──────────────────────────────────────────────────────────┘   │
│                                                                    │
└────────────────────────────────────────────────────────────────────┘
```

### Component Responsibilities

| Component | Verantwoordelijkheid | Locatie |
|-----------|---------------------|---------|
| **AuthHandler** | HTTP request handling, login/logout, token refresh | [`handlers/auth_handler.go`](../handlers/auth_handler.go) |
| **AuthService** | Business logic, JWT generation, password verification | [`services/auth_service.go`](../services/auth_service.go) |
| **AuthMiddleware** | JWT validation, request authentication | [`handlers/middleware.go`](../handlers/middleware.go:12-70) |
| **PermissionMiddleware** | RBAC checks, resource authorization | [`handlers/permission_middleware.go`](../handlers/permission_middleware.go:12-44) |
| **PermissionService** | Permission checks, cache management | [`services/permission_service.go`](../services/permission_service.go:68-110) |
| **GebruikerRepository** | Database operations voor users | [`repository/gebruiker_repository.go`](../repository/gebruiker_repository.go) |
| **RefreshTokenRepository** | Token storage & validation | Database repository |
| **RateLimiter** | Brute force protection | [`services/rate_limiter.go`](../services/rate_limiter.go) |

---

## 🔐 Authentication Flow

### 1. Login Process (Detailed)

```
┌──────────┐
│  Client  │
└────┬─────┘
     │ 1. POST /api/auth/login
     │    { email, wachtwoord }
     ↓
┌────────────────────────────────────────────────────────────┐
│ AuthHandler.HandleLogin                                    │
├────────────────────────────────────────────────────────────┤
│ 1. Parse & validate request body                           │
│ 2. Check rate limiter (max 5 attempts/min per email)      │
│ 3. Call AuthService.Login()                                │
└────┬───────────────────────────────────────────────────────┘
     ↓
┌────────────────────────────────────────────────────────────┐
│ AuthService.Login                                          │
├────────────────────────────────────────────────────────────┤
│ 1. Lookup user by email (case-insensitive)                │
│ 2. Verify user.is_actief = true                           │
│ 3. bcrypt.CompareHashAndPassword(hash, password)          │
│ 4. Update laatste_login timestamp                         │
│ 5. Generate JWT access token (20 min expiry)              │
│ 6. Generate refresh token (7 days, store in DB)           │
│ 7. Return both tokens + user info                         │
└────┬───────────────────────────────────────────────────────┘
     ↓
┌────────────────────────────────────────────────────────────┐
│ JWT Token Generation                                       │
├────────────────────────────────────────────────────────────┤
│ Claims:                                                    │
│ • sub: user.ID (UUID)                                      │
│ • email: user.Email                                        │
│ • role: user.Rol (legacy)                                  │
│ • roles: [array of RBAC role names]                       │
│ • rbac_active: true                                        │
│ • exp: now + 20 minutes                                    │
│ • iat: now                                                 │
│ • iss: "dklemailservice"                                   │
│                                                            │
│ Signing: HS256 with JWT_SECRET                            │
└────┬───────────────────────────────────────────────────────┘
     ↓
┌────────────────────────────────────────────────────────────┐
│ Response to Client                                         │
├────────────────────────────────────────────────────────────┤
│ {                                                          │
│   "success": true,                                         │
│   "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",     │
│   "refresh_token": "base64-encoded-random-32-bytes",       │
│   "user": {                                                │
│     "id": "uuid",                                          │
│     "email": "<EMAIL>",                 │
│     "naam": "Admin",                                       │
│     "rol": "admin",                                        │
│     "permissions": [                                       │
│       {"resource": "admin", "action": "access"},           │
│       {"resource": "contact", "action": "read"}, ...       │
│     ],                                                     │
│     "is_actief": true                                      │
│   }                                                        │
│ }                                                          │
│                                                            │
│ + Set-Cookie: auth_token (HTTPOnly, Secure, SameSite)     │
└────────────────────────────────────────────────────────────┘
```

**Code Reference:** [`handlers/auth_handler.go:29-130`](../handlers/auth_handler.go:29-130)

### 2. Token Validation (Every Protected Request)

```
┌──────────┐
│  Client  │
└────┬─────┘
     │ GET /api/protected-resource
     │ Authorization: Bearer {jwt_token}
     ↓
┌────────────────────────────────────────────────────────────┐
│ AuthMiddleware                                             │
├────────────────────────────────────────────────────────────┤
│ 1. Extract Authorization header                            │
│ 2. Validate "Bearer {token}" format                        │
│ 3. Call AuthService.ValidateToken()                        │
└────┬───────────────────────────────────────────────────────┘
     ↓
┌────────────────────────────────────────────────────────────┐
│ AuthService.ValidateToken                                  │
├────────────────────────────────────────────────────────────┤
│ 1. jwt.ParseWithClaims(token, &JWTClaims{}, ...)          │
│ 2. Verify signing method = HS256                           │
│ 3. Verify signature with JWT_SECRET                        │
│ 4. Check expiration (exp claim)                            │
│ 5. Extract user ID from Subject (sub) claim               │
│ 6. Return userID or error                                  │
└────┬───────────────────────────────────────────────────────┘
     ↓
┌────────────────────────────────────────────────────────────┐
│ Context Storage                                            │
├────────────────────────────────────────────────────────────┤
│ c.Locals("userID", userID)  // Store in Fiber context     │
│ c.Locals("token", token)    // Store original token       │
│                                                            │
│ → Available to all downstream handlers                     │
└────┬───────────────────────────────────────────────────────┘
     ↓
┌────────────────────────────────────────────────────────────┐
│ Next Handler (Permission checks, business logic)           │
└────────────────────────────────────────────────────────────┘
```

**Code Reference:** [`handlers/middleware.go:12-70`](../handlers/middleware.go:12-70)

### 3. Token Refresh Flow

```
┌──────────┐
│  Client  │ (Access token expired after 20 min)
└────┬─────┘
     │ POST /api/auth/refresh
     │ { "refresh_token": "..." }
     ↓
┌────────────────────────────────────────────────────────────┐
│ AuthService.RefreshAccessToken                             │
├────────────────────────────────────────────────────────────┤
│ 1. Lookup refresh_token in database                        │
│ 2. Validate: !is_revoked && expires_at > NOW()            │
│ 3. Get user by token.user_id                               │
│ 4. Verify user.is_actief = true                           │
│ 5. Generate NEW access token (20 min)                      │
│ 6. Generate NEW refresh token (7 days)                     │
│ 7. Revoke OLD refresh token (security)                     │
│ 8. Return both new tokens                                  │
└────┬───────────────────────────────────────────────────────┘
     ↓
┌────────────────────────────────────────────────────────────┐
│ Response                                                   │
├────────────────────────────────────────────────────────────┤
│ {                                                          │
│   "success": true,                                         │
│   "token": "new_access_token",                             │
│   "refresh_token": "new_refresh_token"                     │
│ }                                                          │
│                                                            │
│ Frontend MUST:                                             │
│ 1. Store new tokens in localStorage                        │
│ 2. Update Authorization header for future requests         │
│ 3. Schedule next refresh (15 minutes)                      │
└────────────────────────────────────────────────────────────┘
```

**Token Rotation Security:**
- Oude refresh token wordt automatisch ingetrokken
- Voorkomt replay attacks
- Elke refresh genereert een nieuw token pair

**Code Reference:** [`services/auth_service.go:400-448`](../services/auth_service.go:400-448)

---

## 🛡️ Authorization (RBAC)

### Permission Structure

```typescript
interface Permission {
  resource: string  // Resource naam (bijv. "contact", "user")
  action: string    // Actie (bijv. "read", "write", "delete")
}

// Format in database & API: "resource:action"
// Voorbeelden: "contact:read", "admin:access", "user:manage_roles"
```

### Complete Permission Catalog (58 Permissions)

| Resource | Actions | Beschrijving |
|----------|---------|--------------|
| **admin** | `access` | Volledige admin toegang tot systeem |
| **staff** | `access` | Staff-level toegang (read-only meestal) |
| **contact** | `read`, `write`, `delete` | Contactformulier beheer |
| **aanmelding** | `read`, `write`, `delete` | Registratie/aanmelding beheer |
| **user** | `read`, `write`, `delete`, `manage_roles` | Gebruikersbeheer |
| **photo** | `read`, `write`, `delete` | Foto beheer |
| **album** | `read`, `write`, `delete` | Album beheer |
| **video** | `read`, `write`, `delete` | Video beheer |
| **partner** | `read`, `write`, `delete` | Partner/sponsor beheer |
| **sponsor** | `read`, `write`, `delete` | Sponsor beheer |
| **radio_recording** | `read`, `write`, `delete` | Radio opname beheer |
| **program_schedule** | `read`, `write`, `delete` | Programma schema beheer |
| **social_embed** | `read`, `write`, `delete` | Social media embed beheer |
| **social_link** | `read`, `write`, `delete` | Social media link beheer |
| **under_construction** | `read`, `write`, `delete` | Under construction pagina beheer |
| **newsletter** | `read`, `write`, `send`, `delete` | Newsletter beheer |
| **email** | `read`, `write`, `delete`, `fetch` | Email beheer |
| **admin_email** | `send` | Admin email verzending |
| **chat** | `read`, `write`, `manage_channel`, `moderate` | Chat systeem |

**Totaal:** 19 resources × gemiddeld 3 acties = 58 permissions

### System Roles

| Role | Permissions Count | Use Case | Is System Role |
|------|-------------------|----------|----------------|
| **admin** | 58 (ALL) | Platform administrators | ✅ Yes |
| **staff** | ~15 (mostly read) | Support medewerkers | ✅ Yes |
| **user** | 2 (chat read/write) | Reguliere gebruikers | ✅ Yes |
| **owner** | 4 (full chat control) | Chat channel creators | ✅ Yes |
| **chat_admin** | 3 (moderate, read, write) | Chat moderators | ✅ Yes |
| **member** | 2 (chat read/write) | Chat channel members | ✅ Yes |
| **deelnemer** | 0 | Event participants (categorization only) | ✅ Yes |
| **begeleider** | 0 | Event guides (categorization only) | ✅ Yes |
| **vrijwilliger** | 0 | Volunteers (categorization only) | ✅ Yes |

**System Roles** kunnen niet worden verwijderd via API.

### Permission Check Flow

```
┌──────────┐
│  Client  │
└────┬─────┘
     │ GET /api/contacts (requires contact:read)
     │ Authorization: Bearer {jwt}
     ↓
┌────────────────────────────────────────────────────────────┐
│ AuthMiddleware → Extract & Validate JWT                    │
│ c.Locals("userID", userID)                                 │
└────┬───────────────────────────────────────────────────────┘
     ↓
┌────────────────────────────────────────────────────────────┐
│ PermissionMiddleware("contact", "read")                    │
├────────────────────────────────────────────────────────────┤
│ 1. Get userID from context                                 │
│ 2. Call PermissionService.HasPermission(ctx, userID,       │
│         "contact", "read")                                 │
└────┬───────────────────────────────────────────────────────┘
     ↓
┌────────────────────────────────────────────────────────────┐
│ PermissionService.HasPermission                            │
├────────────────────────────────────────────────────────────┤
│ 1. Check Redis cache: perm:{userID}:contact:read          │
│    ├─ HIT → Return cached boolean (1-2ms)                 │
│    └─ MISS → Continue to step 2                           │
│                                                            │
│ 2. Query database:                                         │
│    SELECT p.resource, p.action                             │
│    FROM user_roles ur                                      │
│    JOIN roles r ON ur.role_id = r.id                       │
│    JOIN role_permissions rp ON r.id = rp.role_id          │
│    JOIN permissions p ON rp.permission_id = p.id          │
│    WHERE ur.user_id = ? AND ur.is_active = true           │
│                                                            │
│ 3. Check if "contact:read" exists in results              │
│                                                            │
│ 4. Cache result in Redis (5 min TTL)                      │
│                                                            │
│ 5. Return boolean                                          │
└────┬───────────────────────────────────────────────────────┘
     ↓
┌────────────────────────────────────────────────────────────┐
│ If FALSE → 403 Forbidden {"error": "Geen toegang"}        │
│ If TRUE  → Continue to handler                             │
└────────────────────────────────────────────────────────────┘
```

**Code Reference:** [`services/permission_service.go:68-110`](../services/permission_service.go:68-110)

---

## 🔧 Components Deep Dive

### 1. AuthHandler ([`handlers/auth_handler.go`](../handlers/auth_handler.go))

**Verantwoordelijkheden:**
- HTTP request parsing & validation
- Rate limiting enforcement
- Response formatting
- Cookie management

**Key Methods:**

```go
type AuthHandler struct {
    authService       services.AuthService
    permissionService services.PermissionService
    rateLimiter       services.RateLimiterService
}

// HandleLogin - Login flow met rate limiting
func (h *AuthHandler) HandleLogin(c *fiber.Ctx) error

// HandleRefreshToken - Token rotation
func (h *AuthHandler) HandleRefreshToken(c *fiber.Ctx) error

// HandleLogout - Clear tokens & cookies
func (h *AuthHandler) HandleLogout(c *fiber.Ctx) error

// HandleGetProfile - User profile + permissions + roles
func (h *AuthHandler) HandleGetProfile(c *fiber.Ctx) error

// HandleResetPassword - Password change (requires auth)
func (h *AuthHandler) HandleResetPassword(c *fiber.Ctx) error
```

**Security Features:**
- Rate limiting: 5 login attempts per minute per email
- Case-insensitive email lookup
- HTTPOnly, Secure, SameSite cookies
- Comprehensive error logging

### 2. AuthService ([`services/auth_service.go`](../services/auth_service.go))

**Verantwoordelijkheden:**
- JWT generation & validation
- Password hashing & verification (bcrypt)
- Token lifecycle management
- User authentication logic

**Key Methods:**

```go
type AuthServiceImpl struct {
    gebruikerRepo    repository.GebruikerRepository
    refreshTokenRepo repository.RefreshTokenRepository
    userRoleRepo     repository.UserRoleRepository
    jwtSecret        []byte
    tokenExpiry      time.Duration
}

// Login - Authenticate user & generate tokens
func (s *AuthServiceImpl) Login(ctx, email, password) (token, refreshToken, error)

// ValidateToken - Verify JWT signature & expiration
func (s *AuthServiceImpl) ValidateToken(token string) (userID string, error)

// GetUserFromToken - Get full user object from JWT
func (s *AuthServiceImpl) GetUserFromToken(ctx, token) (*models.Gebruiker, error)

// RefreshAccessToken - Token rotation with old token revocation
func (s *AuthServiceImpl) RefreshAccessToken(ctx, refreshToken) (newToken, newRefresh, error)

// HashPassword - bcrypt password hashing
func (s *AuthServiceImpl) HashPassword(password string) (string, error)

// VerifyPassword - bcrypt password verification
func (s *AuthServiceImpl) VerifyPassword(hash, password string) bool
```

**JWT Claims Structure:**

```go
type JWTClaims struct {
    Email      string   `json:"email"`
    Role       string   `json:"role"`        // Legacy (backward compat)
    Roles      []string `json:"roles"`       // RBAC role names
    RBACActive bool     `json:"rbac_active"` // RBAC enabled flag
    jwt.RegisteredClaims
}

// RegisteredClaims include:
// - Subject: userID (UUID)
// - ExpiresAt: 20 minutes from now
// - IssuedAt: current time
// - NotBefore: current time
// - Issuer: "dklemailservice"
```

### 3. Middleware Stack ([`handlers/middleware.go`](../handlers/middleware.go))

**AuthMiddleware** (Authenticatie)

```go
func AuthMiddleware(authService services.AuthService) fiber.Handler {
    return func(c *fiber.Ctx) error {
        // 1. Extract "Authorization: Bearer {token}" header
        // 2. Validate token format
        // 3. Verify JWT signature & expiration
        // 4. Store userID in context: c.Locals("userID", userID)
        // 5. Continue or return 401 Unauthorized
    }
}
```

**PermissionMiddleware** (Autorisatie)

```go
func PermissionMiddleware(permService, resource, action string) fiber.Handler {
    return func(c *fiber.Ctx) error {
        // 1. Get userID from context
        // 2. Check permission via PermissionService
        // 3. Continue or return 403 Forbidden
    }
}
```

**Convenience Middlewares:**

```go
// Admin-only endpoints
AdminPermissionMiddleware(permService)
// → Checks "admin:access"

// Staff-level endpoints  
StaffPermissionMiddleware(permService)
// → Checks "staff:access"

// Legacy role-based (deprecated, use RBAC)
AdminMiddleware(authService)
StaffMiddleware(authService)
```

**RateLimitMiddleware** (DDoS Protection)

```go
func RateLimitMiddleware(rateLimiter, keyPrefix string) fiber.Handler {
    // Uses Redis-backed rate limiter
    // Default: 100 requests per minute per IP
}
```

### 4. PermissionService ([`services/permission_service.go`](../services/permission_service.go))

**Verantwoordelijkheden:**
- Permission checks met caching
- Role management (CRUD)
- Permission management (CRUD)
- User-role assignments
- Cache invalidation

**Key Methods:**

```go
type PermissionServiceImpl struct {
    rbacRoleRepo       repository.RBACRoleRepository
    permissionRepo     repository.PermissionRepository
    rolePermissionRepo repository.RolePermissionRepository
    userRoleRepo       repository.UserRoleRepository
    redisClient        *redis.Client
    cacheEnabled       bool
}

// Core permission check (with caching)
func (s *PermissionServiceImpl) HasPermission(ctx, userID, resource, action) bool

// User permissions & roles
func (s *PermissionServiceImpl) GetUserPermissions(ctx, userID) ([]*models.UserPermission, error)
func (s *PermissionServiceImpl) GetUserRoles(ctx, userID) ([]*models.UserRole, error)

// Role management
func (s *PermissionServiceImpl) CreateRole(ctx, role, createdBy) error
func (s *PermissionServiceImpl) UpdateRole(ctx, role) error
func (s *PermissionServiceImpl) DeleteRole(ctx, roleID) error

// Permission-to-role assignments
func (s *PermissionServiceImpl) AssignPermissionToRole(ctx, roleID, permID, assignedBy) error
func (s *PermissionServiceImpl) RevokePermissionFromRole(ctx, roleID, permID) error

// User-role assignments
func (s *PermissionServiceImpl) AssignRole(ctx, userID, roleID, assignedBy) error
func (s *PermissionServiceImpl) RevokeRole(ctx, userID, roleID) error

// Cache management
func (s *PermissionServiceImpl) InvalidateUserCache(userID string)
func (s *PermissionServiceImpl) RefreshCache(ctx context.Context) error
```

**Caching Strategy:**

```go
// Cache key format
key := fmt.Sprintf("perm:%s:%s:%s", userID, resource, action)

// TTL: 5 minutes (balance between freshness & performance)
ttl := 5 * time.Minute

// Automatic invalidation scenarios:
// 1. User role assigned/revoked
// 2. Role permissions changed
// 3. Role deleted
// 4. Manual refresh via API
// 5. TTL expiration
```

---

## 🔒 Security Layers

### Layer 1: Rate Limiting (API Gateway)

```go
// Login endpoint protection
app.Post("/api/auth/login", 
    RateLimitMiddleware(rateLimiter, "login"), 
    authHandler.HandleLogin
)

// Configuration
maxRequests := 5        // Max 5 login attempts
windowDuration := 1 min // Per minute
keyFormat := "login:{email}"
```

**Implementation:** Redis-backed sliding window algorithm

### Layer 2: Authentication (Token Validation)

```go
// All protected routes
protected := api.Group("/", AuthMiddleware(authService))

// Validates:
// 1. Token presence
// 2. Token format (Bearer)
// 3. JWT signature (HS256 + JWT_SECRET)
// 4. Token expiration
// 5. User existence & actief status
```

### Layer 3: Authorization (Permission Checks)

```go
// Resource-specific protection
app.Get("/api/contacts",
    AuthMiddleware(authService),
    PermissionMiddleware(permService, "contact", "read"),
    contactHandler.GetContacts
)

// Checks user_roles → role_permissions → permissions
```

### Layer 4: Cookie Security

```go
cookie := fiber.Cookie{
    Name:     "auth_token",
    Value:    token,
    Path:     "/",
    Expires:  time.Now().Add(20 * time.Minute),
    HTTPOnly: true,          // Prevent XSS attacks
    Secure:   true,          // HTTPS only (production)
    SameSite: "Strict",      // CSRF protection
}
```

### Password Security

```go
// Hashing (registration/password change)
cost := bcrypt.DefaultCost  // 10 rounds
hash, _ := bcrypt.GenerateFromPassword([]byte(password), cost)

// Verification (login)
err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
valid := (err == nil)

// Minimum Requirements (recommended):
// - Length: 8+ characters
// - Complexity: uppercase, lowercase, number, special char
```

---

## 📡 API Endpoints

### Authentication Endpoints

| Endpoint | Method | Auth Required | Rate Limited | Beschrijving |
|----------|--------|---------------|--------------|--------------|
| `/api/auth/login` | POST | ❌ | ✅ (5/min) | Login met email + wachtwoord |
| `/api/auth/logout` | POST | ❌ | ❌ | Logout (clear cookies) |
| `/api/auth/refresh` | POST | ❌ | ❌ | Refresh access token |
| `/api/auth/profile` | GET | ✅ | ❌ | Get user profile + permissions |
| `/api/auth/reset-password` | POST | ✅ | ❌ | Change password |

### RBAC Management Endpoints

| Endpoint | Method | Permission | Beschrijving |
|----------|--------|-----------|--------------|
| `/api/rbac/permissions` | GET | `admin:access` | List all permissions |
| `/api/rbac/permissions` | POST | `admin:access` | Create permission |
| `/api/rbac/permissions/:id` | DELETE | `admin:access` | Delete permission |
| `/api/rbac/roles` | GET | `admin:access` | List all roles |
| `/api/rbac/roles/:id` | GET | `admin:access` | Get role details |
| `/api/rbac/roles` | POST | `admin:access` | Create role |
| `/api/rbac/roles/:id` | PUT | `admin:access` | Update role |
| `/api/rbac/roles/:id` | DELETE | `admin:access` | Delete role |
| `/api/rbac/roles/:id/permissions` | PUT | `admin:access` | Bulk assign permissions |
| `/api/rbac/roles/:roleId/permissions/:permId` | POST | `admin:access` | Assign permission |
| `/api/rbac/roles/:roleId/permissions/:permId` | DELETE | `admin:access` | Remove permission |
| `/api/rbac/cache/refresh` | POST | `admin:access` | Invalidate cache |

### User Management Endpoints

| Endpoint | Method | Permission | Beschrijving |
|----------|--------|-----------|--------------|
| `/api/users/:userId/roles` | GET | `user:read` | Get user roles |
| `/api/users/:userId/roles` | POST | `user:manage_roles` | Assign role |
| `/api/users/:userId/roles/:roleId` | DELETE | `user:manage_roles` | Remove role |
| `/api/users/:userId/permissions` | GET | `user:read` | Get effective permissions |

### Example Request/Response

**Login:**
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "wachtwoord": "SecurePassword123!"
}
```

```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************.signature",
  "refresh_token": "YmFzZTY0LWVuY29kZWQtcmFuZG9tLTMyLWJ5dGVz",
  "user": {
    "id": "12345678-1234-1234-1234-123456789",
    "email": "<EMAIL>",
    "naam": "Admin User",
    "rol": "admin",
    "permissions": [
      {"resource": "admin", "action": "access"},
      {"resource": "contact", "action": "read"},
      {"resource": "contact", "action": "write"}
    ],
    "is_actief": true
  }
}
```

**Protected Request:**
```http
GET /api/contacts
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Error Responses:**

| Status | Code | Scenario |
|--------|------|----------|
| 400 | - | Invalid request body |
| 401 | `NO_AUTH_HEADER` | Missing Authorization header |
| 401 | `INVALID_AUTH_HEADER` | Invalid header format |
| 401 | `TOKEN_EXPIRED` | JWT expired (> 20 min) |
| 401 | `TOKEN_MALFORMED` | Invalid JWT structure |
| 401 | `TOKEN_SIGNATURE_INVALID` | Wrong JWT secret |
| 401 | `REFRESH_TOKEN_INVALID` | Invalid/expired refresh token |
| 403 | - | Permission denied |
| 429 | - | Rate limit exceeded |

---

## 🗄️ Database Schema

### Core RBAC Tables

```sql
-- Roles (system & custom)
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT FALSE,  -- Cannot be deleted
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by UUID REFERENCES gebruikers(id)
);

-- Permissions (granular actions)
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    is_system_permission BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(resource, action)
);

-- Role-Permission mapping
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT NOW(),
    assigned_by UUID REFERENCES gebruikers(id),
    PRIMARY KEY(role_id, permission_id)
);

-- User-Role mapping
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES gebruikers(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT NOW(),
    assigned_by UUID REFERENCES gebruikers(id),
    expires_at TIMESTAMP,                  -- Optional expiration
    is_active BOOLEAN DEFAULT TRUE,        -- Can be disabled
    UNIQUE(user_id, role_id)
);

-- Refresh Tokens (7-day lifetime)
CREATE TABLE refresh_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES gebruikers(id) ON DELETE CASCADE,
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    is_revoked BOOLEAN DEFAULT FALSE,
    revoked_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_token ON refresh_tokens(token);
CREATE INDEX idx_refresh_tokens_expires_at ON refresh_tokens(expires_at);

-- Users table
CREATE TABLE gebruikers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    naam VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    wachtwoord_hash VARCHAR(255) NOT NULL,
    rol VARCHAR(50) DEFAULT 'gebruiker',   -- Legacy field
    is_actief BOOLEAN DEFAULT TRUE,
    newsletter_subscribed BOOLEAN DEFAULT FALSE,
    laatste_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_gebruikers_email ON gebruikers(LOWER(email));
CREATE INDEX idx_gebruikers_rol ON gebruikers(rol);
```

### Helpful Database Views

```sql
-- View: User permissions (flattened)
CREATE VIEW user_permissions_view AS
SELECT 
    ur.user_id,
    g.email,
    r.name AS role_name,
    p.resource,
    p.action,
    rp.assigned_at AS permission_assigned_at,
    ur.assigned_at AS role_assigned_at
FROM user_roles ur
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
JOIN gebruikers g ON ur.user_id = g.id
WHERE ur.is_active = TRUE
  AND (ur.expires_at IS NULL OR ur.expires_at > NOW());

-- Query specific user permissions
SELECT * FROM user_permissions_view 
WHERE email = '<EMAIL>'
ORDER BY resource, action;
```

### Database Migrations

**Required Migrations:**
- `V1_20__create_rbac_tables.sql` - RBAC infrastructure
- `V1_21__seed_rbac_data.sql` - Default roles & permissions
- `V1_22__migrate_legacy_roles_to_rbac.sql` - Legacy migration
- `V1_28__add_refresh_tokens.sql` - Refresh token support

**Migration Location:** [`database/migrations/`](../database/migrations/)

---

## 💾 Caching Strategy

### Redis Configuration

```go
// Connection
redisClient := redis.NewClient(&redis.Options{
    Addr:     os.Getenv("REDIS_HOST") + ":" + os.Getenv("REDIS_PORT"),
    Password: os.Getenv("REDIS_PASSWORD"),
    DB:       0,
})

// Health check
ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
defer cancel()
if _, err := redisClient.Ping(ctx).Result(); err != nil {
    logger.Warn("Redis unavailable, disabling cache")
    cacheEnabled = false
}
```

### Cache Key Format

```
perm:{userID}:{resource}:{action}

Examples:
perm:12345678-1234-1234-1234-123456789:contact:read
perm:87654321-4321-4321-4321-987654321:admin:access
perm:abcdef12-5678-90ab-cdef-1234567890ab:user:manage_roles
```

### Cache Operations

**Set (after DB query):**
```go
cacheKey := fmt.Sprintf("perm:%s:%s:%s", userID, resource, action)
data, _ := json.Marshal(hasPermission)
redisClient.Set(ctx, cacheKey, data, 5*time.Minute).Err()
```

**Get (before DB query):**
```go
cacheKey := fmt.Sprintf("perm:%s:%s:%s", userID, resource, action)
val, err := redisClient.Get(ctx, cacheKey).Result()
if err == redis.Nil {
    // Cache miss → Query database
}
var hasPermission bool
json.Unmarshal([]byte(val), &hasPermission)
return hasPermission
```

**Invalidate (on role changes):**
```go
// Single user
pattern := fmt.Sprintf("perm:%s:*", userID)
keys, _ := redisClient.Keys(ctx, pattern).Result()
redisClient.Del(ctx, keys...)

// All users (full refresh)
pattern := "perm:*"
keys, _ := redisClient.Keys(ctx, pattern).Result()
redisClient.Del(ctx, keys...)
```

### Cache TTL Strategy

| TTL | Rationale |
|-----|-----------|
| **5 minutes** | Balance between freshness & performance |
| **Pros** | Ultra-fast permission checks (1-2ms), 97% hit rate |
| **Cons** | Up to 5 min delay for permission changes |
| **Mitigation** | Manual cache refresh API endpoint for immediate updates |

### Cache Invalidation Triggers

1. **User role assigned** → Invalidate user cache
2. **User role revoked** → Invalidate user cache
3. **Role deleted** → Full cache refresh
4. **Permission added to role** → Invalidate all users with that role
5. **Permission removed from role** → Invalidate all users with that role
6. **Manual refresh** → Admin API endpoint
7. **TTL expiration** → Automatic after 5 minutes

---

## 🚦 Route Protection Patterns

### Pattern 1: Authentication Only

```go
// Only verify user is logged in (no permission check)
protected := api.Group("/", AuthMiddleware(authService))
protected.Get("/profile", authHandler.HandleGetProfile)
```

**Use Case:** User profile, logout, basic authenticated endpoints

### Pattern 2: Authentication + Single Permission

```go
// Verify authentication + specific permission
api.Get("/contacts",
    AuthMiddleware(authService),
    PermissionMiddleware(permService, "contact", "read"),
    contactHandler.GetContacts
)
```

**Use Case:** Most CRUD operations

### Pattern 3: Authentication + Multiple Permissions

```go
// Verify authentication + multiple permissions (ALL required)
api.Post("/contacts/:id/critical-action",
    AuthMiddleware(authService),
    ResourcePermissionMiddleware(permService, 
        models.PermissionCheck{Resource: "contact", Action: "write"},
        models.PermissionCheck{Resource: "admin", Action: "access"},
    ),
    contactHandler.CriticalAction
)
```

**Use Case:** Critical operations requiring multiple authorities

### Pattern 4: Admin-Only Shortcut

```go
// Convenience: admin:access permission
admin := api.Group("/admin",
    AuthMiddleware(authService),
    AdminPermissionMiddleware(permService)
)
admin.Get("/dashboard", adminHandler.Dashboard)
```

**Use Case:** Admin panel endpoints

### Pattern 5: Staff+ Access

```go
// Convenience: staff:access OR admin:access
staff := api.Group("/staff",
    AuthMiddleware(authService),
    StaffPermissionMiddleware(permService)
)
staff.Get("/reports", staffHandler.Reports)
```

**Use Case:** Support staff + admin endpoints

### Pattern 6: Public (No Protection)

```go
// No authentication or authorization required
api.Get("/public/partners", partnerHandler.GetPublicPartners)
```

**Use Case:** Public website data

### Route Registration Example ([`main.go`](../main.go))

```go
// Public routes
api.Post("/contact-email", emailHandler.HandleContactEmail)
api.Post("/aanmelding-email", emailHandler.HandleAanmeldingEmail)

// Auth routes (no prior auth required)
auth := api.Group("/auth")
auth.Post("/login", RateLimitMiddleware(rateLimiter, "login"), authHandler.HandleLogin)
auth.Post("/refresh", authHandler.HandleRefreshToken)

// Protected auth routes (require authentication)
authProtected := auth.Group("/", AuthMiddleware(authService))
authProtected.Get("/profile", authHandler.HandleGetProfile)

// Contact management (authentication + permissions)
contactHandler.RegisterRoutes(app)
// → Internally uses PermissionMiddleware("contact", "read/write/delete")

// Admin RBAC management (authentication + admin permission)
permissionHandler.RegisterRoutes(app)
// → Internally uses AdminPermissionMiddleware
```

---

## ⚠️ Error Handling

### Error Types

**Authentication Errors (401 Unauthorized):**
```json
{
  "error": "Niet geautoriseerd",
  "code": "NO_AUTH_HEADER"
}
```

| Error Code | Betekenis | Oplossing |
|-----------|-----------|-----------|
| `NO_AUTH_HEADER` | Missing Authorization header | Include header in request |
| `INVALID_AUTH_HEADER` | Wrong format (not "Bearer {token}") | Fix header format |
| `TOKEN_EXPIRED` | JWT expired (> 20 min) | Use refresh token to get new access token |
| `TOKEN_MALFORMED` | Invalid JWT structure | Re-login to get valid token |
| `TOKEN_SIGNATURE_INVALID` | Wrong JWT secret | Backend misconfiguration |
| `REFRESH_TOKEN_INVALID` | Invalid/expired refresh token | Re-login required |

**Authorization Errors (403 Forbidden):**
```json
{
  "error": "Geen toegang"
}
```

**Betekenis:** Valid authentication maar insufficient permissions

**Rate Limit Errors (429 Too Many Requests):**
```json
{
  "error": "Te veel verzoeken, probeer het later opnieuw"
}
```

**Betekenis:** Exceeded rate limit (5 login attempts/min)

### Backend Error Logging

```go
// Authentication failures
logger.Warn("Token validatie gefaald",
    "error", err,
    "code", errorCode,
    "path", c.Path(),
    "ip", c.IP()
)

// Permission denials
logger.Warn("Permission denied",
    "user_id", userID,
    "resource", resource,
    "action", action,
    "path", c.Path(),
    "method", c.Method()
)

// Rate limit hits
logger.Warn("Rate limit overschreden",
    "ip", ip,
    "key", key
)
```

### Frontend Error Handling (Recommended)

```typescript
try {
  const response = await fetch('/api/contacts', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  if (response.status === 401) {
    const data = await response.json();
    if (data.code === 'TOKEN_EXPIRED') {
      // Try refresh token
      await refreshAccessToken();
      // Retry request
    } else {
      // Redirect to login
      navigate('/login');
    }
  } else if (response.status === 403) {
    // Show "Geen toegang" message
    showError('Je hebt geen toegang tot deze resource');
  } else if (response.status === 429) {
    // Show rate limit message
    showError('Te veel verzoeken, probeer het later opnieuw');
  }
} catch (error) {
  // Network error
  showError('Netwerkfout, controleer je internetverbinding');
}
```

---

## 📊 Performance & Monitoring

### Performance Metrics

| Metric | Target | Actual | Measurement Method |
|--------|--------|--------|-------------------|
| Login Response Time | < 500ms | ~300ms | Handler execution time |
| Token Validation | < 10ms | ~5ms | JWT parse + verify |
| Permission Check (cached) | < 5ms | ~2ms | Redis GET |
| Permission Check (uncached) | < 50ms | ~30ms | DB query + cache set |
| Token Refresh | < 200ms | ~150ms | DB operations + JWT generation |
| Cache Hit Rate | > 95% | ~97% | Redis metrics |

### Monitoring Endpoints

**Health Check:**
```http
GET /api/health

Response:
{
  "status": "healthy",
  "version": "v1.48.0",
  "database": "connected",
  "redis": "connected",
  "uptime": "72h30m15s",
  "rate_limiter": {
    "status": "operational",
    "active_keys": 42
  }
}
```

**Prometheus Metrics:**
```http
GET /metrics

# HELP auth_login_total Total number of login attempts
# TYPE auth_login_total counter
auth_login_total{status="success"} 1243
auth_login_total{status="failure"} 87

# HELP auth_token_validation_duration_seconds Token validation duration
# TYPE auth_token_validation_duration_seconds histogram
auth_token_validation_duration_seconds_bucket{le="0.005"} 9821
auth_token_validation_duration_seconds_bucket{le="0.01"} 9876

# HELP permission_check_total Total permission checks
# TYPE permission_check_total counter
permission_check_total{cached="true",result="granted"} 45678
permission_check_total{cached="false",result="granted"} 1234

# HELP redis_cache_hit_rate Permission cache hit rate
# TYPE redis_cache_hit_rate gauge
redis_cache_hit_rate 0.97
```

### Logging Configuration

```go
// Log levels (set via LOG_LEVEL env var)
logLevel := os.Getenv("LOG_LEVEL")
// Options: "DEBUG", "INFO", "WARN", "ERROR"

logger.Setup(logLevel)

// ELK Integration (optional)
elkEndpoint := os.Getenv("ELK_ENDPOINT")
if elkEndpoint != "" {
    logger.SetupELK(logger.ELKConfig{
        Endpoint:      elkEndpoint,
        BatchSize:     100,
        FlushInterval: 5 * time.Second,
        AppName:       "dklemailservice",
        Environment:   os.Getenv("ENVIRONMENT"),
    })
}
```

### Key Metrics to Monitor

1. **Authentication:**
   - Login success/failure rate
   - Token validation errors
   - Token refresh rate
   - Average login duration

2. **Authorization:**
   - Permission check frequency
   - Permission denial rate
   - Cache hit rate
   - Cache invalidation frequency

3. **Security:**
   - Rate limit hits
   - Failed login attempts per IP
   - Unusual access patterns
   - Token expiration issues

4. **System Health:**
   - Database connection status
   - Redis connection status
   - API response times
   - Error rates by endpoint

---

## ✅ Deployment Checklist

### Pre-Deployment

**Environment Variables:**
```bash
# Required
JWT_SECRET=<minimum-32-character-random-string>
JWT_TOKEN_EXPIRY=20m

DATABASE_URL=postgresql://user:pass@host:port/db
DB_SSL_MODE=require

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=<optional>

CORS_ORIGIN=https://admin.dekoninklijkeloop.nl

# Optional
LOG_LEVEL=INFO
ENVIRONMENT=production
ELK_ENDPOINT=https://elk.example.com
```

**Security Checks:**
- [ ] JWT_SECRET is strong (32+ random characters)
- [ ] JWT_SECRET matches between deployments
- [ ] Database SSL enabled in production
- [ ] Redis password configured
- [ ] CORS origins restricted to known domains
- [ ] Rate limiting enabled
- [ ] HTTPOnly cookies enabled
- [ ] Secure cookies enabled (HTTPS)

**Database Checks:**
- [ ] All migrations applied (V1.20 - V1.48+)
- [ ] RBAC tables exist
- [ ] Default roles seeded (admin, staff, user, etc.)
- [ ] Default permissions seeded (58 permissions)
- [ ] Database connection stable
- [ ] Backup strategy in place

**Redis Checks:**
- [ ] Redis server running
- [ ] Redis accessible from application
- [ ] Memory limit configured
- [ ] Persistence configured (optional)

### Deployment Steps

1. **Deploy Backend:**
   ```bash
   # Build Go application
   go build -o dklemailservice main.go
   
   # Run database migrations
   ./dklemailservice migrate
   
   # Start service
   ./dklemailservice
   ```

2. **Verify Health:**
   ```bash
   curl https://api.dekoninklijkeloop.nl/api/health
   ```

3. **Test Authentication:**
   ```bash
   # Login test
   curl -X POST https://api.dekoninklijkeloop.nl/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","wachtwoord":"password"}'
   
   # Profile test (use token from login response)
   curl https://api.dekoninklijkeloop.nl/api/auth/profile \
     -H "Authorization: Bearer {token}"
   ```

4. **Test Permission System:**
   ```bash
   # Admin endpoint (should succeed for admin user)
   curl https://api.dekoninklijkeloop.nl/api/rbac/permissions \
     -H "Authorization: Bearer {admin_token}"
   
   # Admin endpoint (should fail for regular user)
   curl https://api.dekoninklijkeloop.nl/api/rbac/permissions \
     -H "Authorization: Bearer {regular_user_token}"
   # Expected: 403 Forbidden
   ```

5. **Verify Cache:**
   ```bash
   # Check Redis connection
   redis-cli -h <host> PING
   
   # Monitor cache keys
   redis-cli -h <host> KEYS "perm:*"
   ```

### Post-Deployment

**Monitoring Setup:**
- [ ] Prometheus scraping `/metrics`
- [ ] ELK log aggregation (if configured)
- [ ] Health check alerts
- [ ] Error rate alerts
- [ ] Performance degradation alerts

**Documentation:**
- [ ] API documentation updated
- [ ] Frontend integration guide shared
- [ ] Deployment runbook created
- [ ] Rollback procedure documented

---

## 🔧 Troubleshooting

### "Backend returned no permissions"

**Symptoms:**
- User can login but has no permissions
- Frontend shows empty permission array
- All protected routes return 403

**Diagnosis:**
```typescript
// Check user object
const { user } = useAuth();
console.log('Permissions:', user?.permissions);
console.log('Roles:', user?.roles);

// Check API response
fetch('/api/auth/profile', {
  headers: { 'Authorization': `Bearer ${token}` }
}).then(r => r.json()).then(console.log);
```

**Database Verification:**
```sql
-- Check user roles
SELECT * FROM user_roles WHERE user_id = '<user-uuid>';

-- Check role permissions
SELECT r.name, p.resource, p.action
FROM user_roles ur
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE ur.user_id = '<user-uuid>' AND ur.is_active = TRUE;
```

**Solutions:**
1. Assign roles via Admin panel
2. Verify role has permissions: `SELECT * FROM role_permissions WHERE role_id = '<role-uuid>'`
3. Check user_role is active: `SELECT is_active FROM user_roles WHERE user_id = '<user-uuid>'`
4. Re-login to refresh JWT claims

### "401 Unauthorized" after login

**Symptoms:**
- Login succeeds
- Subsequent requests fail with 401
- Token appears valid

**Diagnosis:**
```bash
# Decode JWT token (use https://jwt.io)
echo "<token>" | base64 -d

# Check token expiration
# exp claim should be > current Unix timestamp

# Verify JWT_SECRET matches
# Backend logs should not show "Invalid signature"
```

**Solutions:**
1. Check token storage:
   ```typescript
   localStorage.getItem('token')
   localStorage.getItem('refresh_token')
   ```
2. Verify Authorization header format: `Bearer {token}` (space after Bearer)
3. Check JWT_SECRET environment variable matches
4. Verify token not expired (20 min lifetime)
5. Try token refresh

### "Permission denied after role change"

**Symptoms:**
- Admin assigns new role
- User still can't access resources
- Cache not updating

**Cause:** Redis cache (5 min TTL)

**Solutions:**
1. **Instant (Admin):** 
   ```http
   POST /api/rbac/cache/refresh
   Authorization: Bearer {admin_token}
   ```

2. **Wait:** 5 minutes for automatic cache expiration

3. **Re-login:** Forces fresh permission load

4. **Manual (Redis CLI):**
   ```bash
   redis-cli -h <host> DEL "perm:<user-id>:*"
   ```

### "Token refresh fails"

**Symptoms:**
- Auto-logout after 20 minutes
- `/api/auth/refresh` returns 401
- "Ongeldige of verlopen refresh token"

**Diagnosis:**
```sql
-- Check token in database
SELECT * FROM refresh_tokens 
WHERE token = '<refresh-token>'
  AND user_id = '<user-id>';

-- Verify not revoked and not expired
SELECT 
  is_revoked,
  expires_at,
  (expires_at > NOW()) AS still_valid
FROM refresh_tokens
WHERE token = '<refresh-token>';
```

**Solutions:**
1. Check token exists in DB
2. Verify `is_revoked = false`
3. Verify `expires_at > NOW()`
4. Check user is still active: `SELECT is_actief FROM gebruikers WHERE id = '<user-id>'`
5. If all else fails: re-login

### "Rate limit errors"

**Symptoms:**
- 429 Too Many Requests
- "Te veel verzoeken"
- Can't login

**Diagnosis:**
```bash
# Check Redis rate limit keys
redis-cli -h <host> KEYS "login:*"

# View specific key TTL
redis-cli -h <host> TTL "login:<email>"
```

**Solutions:**
1. Wait 1 minute for rate limit reset
2. Verify correct email (rate limit is per email)
3. Check for automated scripts hitting endpoint
4. Adjust rate limit if legitimate use:
   ```go
   // In code (requires redeploy)
   rateLimiter.Configure(keyPrefix, maxRequests, windowDuration)
   ```

### "Database connection issues"

**Symptoms:**
- "Database initialisatie fout"
- Login fails with 500 error
- Health check shows database disconnected

**Diagnosis:**
```bash
# Test database connection
psql -h <host> -p <port> -U <user> -d <dbname>

# Check environment variables
echo $DATABASE_URL
echo $DB_HOST
echo $DB_PORT
```

**Solutions:**
1. Verify database credentials
2. Check SSL requirements: `DB_SSL_MODE=require`
3. Verify network connectivity
4. Check database server logs
5. Verify migrations completed:
   ```sql
   SELECT * FROM migraties ORDER BY versie DESC LIMIT 10;
   ```

---

## 💡 Best Practices

### Security Best Practices

1. **Never Trust Client Data:**
   ```go
   // ❌ BAD: Trust JWT claims for permissions
   claims := parseTokenClaims(c)
   if claims.Role == "admin" { // Attacker can forge this!
       // Allow action
   }
   
   // ✅ GOOD: Always check server-side
   userID := c.Locals("userID").(string)
   if permissionService.HasPermission(ctx, userID, "admin", "access") {
       // Allow action
   }
   ```

2. **Always Use HTTPS in Production:**
   ```go
   cookie := fiber.Cookie{
       Secure: os.Getenv("ENVIRONMENT") == "production", // HTTPS only
       HTTPOnly: true,
       SameSite: "Strict",
   }
   ```

3. **Rotate Secrets Regularly:**
   - JWT_SECRET should be rotated periodically
   - Use strong random strings (32+ characters)
   - Store secrets in secure vault (not in code)

4. **Rate Limit Aggressively:**
   - Login: 5 attempts/min per email
   - Password reset: 3 attempts/hour per email
   - API: 100 requests/min per IP

5. **Log Security Events:**
   ```go
   logger.Warn("Meerdere gefaalde login pogingen",
       "email", email,
       "ip", c.IP(),
       "attempts", attemptCount
   )
   ```

### Performance Best Practices

1. **Cache Permission Checks:**
   ```go
   // ✅ Automatic caching in PermissionService
   hasPermission := permissionService.HasPermission(ctx, userID, resource, action)
   // → Cached for 5 minutes
   ```

2. **Minimize Database Queries:**
   ```go
   // ❌ BAD: N+1 query problem
   for _, user := range users {
       permissions := permissionService.GetUserPermissions(ctx, user.ID)
   }
   
   // ✅ GOOD: Batch query
   allPermissions := permissionService.GetAllUsersPermissions(ctx, userIDs)
   ```

3. **Use Connection Pooling:**
   ```go
   // Configured in config/database.go
   db.SetMaxOpenConns(25)
   db.SetMaxIdleConns(5)
   db.SetConnMaxLifetime(5 * time.Minute)
   ```

4. **Optimize Redis Operations:**
   ```go
   // ✅ Use pipelining for multiple operations
   pipe := redisClient.Pipeline()
   pipe.Set(ctx, key1, val1, ttl)
   pipe.Set(ctx, key2, val2, ttl)
   pipe.Exec(ctx)
   ```

### Code Organization Best Practices

1. **Separation of Concerns:**
   - **Handler:** HTTP concerns (parsing, validation, responses)
   - **Service:** Business logic (authentication, authorization)
   - **Repository:** Database operations (CRUD, queries)

2. **Middleware Chaining:**
   ```go
   // Clear, explicit protection layers
   app.Get("/api/contacts",
       AuthMiddleware(authService),           // Layer 1: Auth
       PermissionMiddleware(permService, ...), // Layer 2: Authz
       contactHandler.GetContacts              // Layer 3: Business logic
   )
   ```

3. **Error Handling:**
   ```go
   // ✅ Consistent error responses
   if err != nil {
       logger.Error("Operation failed", "error", err)
       return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
           "error": "Beschrijvende foutmelding voor gebruiker",
       })
   }
   ```

4. **Configuration Management:**
   ```go
   // ✅ Use environment variables, not hardcoded values
   jwtSecret := os.Getenv("JWT_SECRET")
   tokenExpiry := time.ParseDuration(os.Getenv("JWT_TOKEN_EXPIRY"))
   ```

### Testing Best Practices

1. **Unit Tests:**
   ```go
   func TestAuthService_Login(t *testing.T) {
       // Test valid credentials
       // Test invalid credentials
       // Test inactive user
       // Test rate limiting
   }
   ```

2. **Integration Tests:**
   ```go
   func TestAuthFlow_EndToEnd(t *testing.T) {
       // 1. Login
       // 2. Access protected endpoint
       // 3. Token refresh
       // 4. Logout
   }
   ```

3. **Permission Tests:**
   ```sql
   -- Verify permission structure
   SELECT COUNT(*) FROM permissions; -- Should be 58
   SELECT COUNT(*) FROM roles WHERE is_system_role = TRUE; -- Should be 9
   ```

---

## 📚 Related Documentation

- [`AUTH_AND_RBAC.md`](AUTH_AND_RBAC.md) - Complete RBAC guide
- [`DATABASE_REFERENCE.md`](DATABASE_REFERENCE.md) - Database schema details
- [`FRONTEND_INTEGRATION.md`](FRONTEND_INTEGRATION.md) - Frontend implementation guide
- [Database Migrations](../database/migrations/) - SQL migration files
- [Handler Source Code](../handlers/)
- [Service Source Code](../services/)
- [Repository Source Code](../repository/)

---

## ⚠️ Critical Reminders

> **Frontend checks zijn voor UX (User Experience)**  
> **Backend checks zijn voor Security (Data Protection)**
>
> VERTROUW NOOIT alleen frontend checks voor beveiliging!

### Security Checklist

- ✅ JWT tokens zijn signed met HS256 + JWT_SECRET
- ✅ Tokens verlopen na 20 minuten
- ✅ Refresh tokens verlopen na 7 dagen en worden geroteerd
- ✅ Passwords worden gehashed met bcrypt (cost 10)
- ✅ Rate limiting is actief op login endpoint
- ✅ Alle protected routes hebben AuthMiddleware
- ✅ Permission checks gebeuren server-side
- ✅ Redis cache wordt geïnvalideerd bij role changes
- ✅ Cookies zijn HTTPOnly, Secure, en SameSite

### Performance Metrics

- ✅ Login: ~300ms (incl. permission load)
- ✅ Token validation: ~5ms
- ✅ Permission check (cached): ~2ms
- ✅ Permission check (uncached): ~30ms
- ✅ Cache hit rate: ~97%

---

**Version:** 1.0  
**Last Updated:** 2025-11-01  
**Status:** ✅ Production Ready  
**Backend Compatibility:** V1.48.0+  
**Complete Analysis Date:** 2025-11-01