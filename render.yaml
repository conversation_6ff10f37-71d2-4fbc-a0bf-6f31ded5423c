services:
  - type: web
    name: dklautomatie-backend
    env: go
    buildCommand: go build -o server
    startCommand: ./server
    envVars:
      - key: ADMIN_EMAIL
        sync: false
      - key: SMTP_HOST
        sync: false
      - key: SMTP_PORT
        sync: false
      - key: SMTP_USER
        sync: false
      - key: SMTP_PASSWORD
        sync: false
      - key: ALLOWED_ORIGINS
        sync: false
      - key: SMTP_FROM
        sync: false
      - key: APP_ENV
        value: prod
        sync: false
      # Registratie SMTP configuratie
      - key: REGISTRATION_SMTP_HOST
        sync: false
      - key: REGISTRATION_SMTP_PORT
        sync: false
      - key: REG<PERSON><PERSON><PERSON>ION_SMTP_USER
        sync: false
      - key: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_SMTP_PASSWORD
        sync: false
      - key: REGISTRATION_SMTP_FROM
        sync: false
      - key: REGISTRATION_EMAIL
        sync: false
      # JWT configuratie
      - key: JWT_SECRET
        sync: false
      # Debug logging
      - key: LOG_LEVEL
        value: debug
    healthCheckPath: /api/health
    
  - type: pserv
    name: dklautomatie-db
    engine: postgres
    version: 14
    ipAllowList: []
    plan: free
    autoDeploy: true
    envVars:
      - key: POSTGRES_USER
        generateValue: true
      - key: POSTGRES_PASSWORD
        generateValue: true
      - key: POSTGRES_DB
        value: dklemailservice

  - type: redis
    name: dklautomatie-redis
    ipAllowList: []
    plan: free
    autoDeploy: true