<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DKL Image Upload - Vanilla JS Example</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .upload-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .upload-section:hover,
        .upload-section.drag-active {
            border-color: #007bff;
            background: #f8f9fa;
        }

        .upload-section.uploading {
            pointer-events: none;
            opacity: 0.7;
        }

        .file-input {
            display: block;
            margin: 20px auto;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-width: 300px;
        }

        .upload-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background-color 0.2s;
        }

        .upload-btn:hover:not(:disabled) {
            background: #0056b3;
        }

        .upload-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .progress-container {
            margin-top: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            font-size: 14px;
            color: #666;
        }

        .results {
            margin-top: 30px;
        }

        .image-result {
            display: inline-block;
            margin: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .image-result img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 4px;
            display: block;
            margin-bottom: 10px;
        }

        .image-info {
            font-size: 12px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }

        .caption-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .chat-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .chat-messages {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 20px;
            background: white;
        }

        .chat-message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .chat-message img {
            max-width: 200px;
            border-radius: 4px;
        }

        .chat-message .caption {
            margin-top: 5px;
            font-style: italic;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DKL Image Upload Demo</h1>

        <!-- Authentication Token Input -->
        <div class="upload-section">
            <h3>Authentication</h3>
            <input
                type="text"
                id="authToken"
                placeholder="Enter JWT token"
                class="caption-input"
            >
            <small>Enter your JWT authentication token to use the upload features.</small>
        </div>

        <!-- Single Image Upload -->
        <div class="upload-section" id="singleUpload">
            <h3>Single Image Upload</h3>
            <input type="file" accept="image/*" id="singleFile" class="file-input">
            <button onclick="uploadSingleImage()" class="upload-btn" id="singleBtn">
                Upload Image
            </button>
            <div class="progress-container" id="singleProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="singleProgressFill"></div>
                </div>
                <div class="progress-text" id="singleProgressText">Uploading... 0%</div>
            </div>
        </div>

        <!-- Batch Image Upload -->
        <div class="upload-section" id="batchUpload">
            <h3>Batch Image Upload (Max 5)</h3>
            <input type="file" accept="image/*" multiple id="batchFiles" class="file-input">
            <button onclick="uploadBatchImages()" class="upload-btn" id="batchBtn">
                Upload Images
            </button>
            <div class="progress-container" id="batchProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="batchProgressFill"></div>
                </div>
                <div class="progress-text" id="batchProgressText">Uploading... 0%</div>
            </div>
        </div>

        <!-- Chat Image Upload -->
        <div class="chat-section">
            <h3>Chat Image Upload</h3>
            <div class="chat-messages" id="chatMessages">
                <!-- Chat messages will appear here -->
            </div>
            <input
                type="text"
                id="chatCaption"
                placeholder="Add a caption (optional)"
                class="caption-input"
            >
            <input type="file" accept="image/*" id="chatFile" class="file-input">
            <button onclick="sendChatImage()" class="upload-btn" id="chatBtn">
                Send Image
            </button>
        </div>

        <!-- Results -->
        <div class="results" id="results"></div>

        <!-- Status Messages -->
        <div id="status"></div>
    </div>

    <!-- Include the client library -->
    <script src="./image-upload-client.js"></script>
    <script>
        // Initialize client
        let client = null;
        let authToken = '';

        // DOM elements
        const authTokenInput = document.getElementById('authToken');
        const resultsDiv = document.getElementById('results');
        const statusDiv = document.getElementById('status');

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Load saved token
            const savedToken = localStorage.getItem('dklAuthToken');
            if (savedToken) {
                authTokenInput.value = savedToken;
                updateClient(savedToken);
            }

            // Update client when token changes
            authTokenInput.addEventListener('input', function(e) {
                updateClient(e.target.value);
                localStorage.setItem('dklAuthToken', e.target.value);
            });
        });

        function updateClient(token) {
            authToken = token;
            client = new ImageUploadClient({
                apiBaseUrl: '/api',
                authToken: token
            });
        }

        function showStatus(message, type = 'info') {
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }

        function showError(message) {
            showStatus(message, 'error');
        }

        function showSuccess(message) {
            showStatus(message, 'success');
        }

        function setUploading(sectionId, uploading) {
            const section = document.getElementById(sectionId);
            const button = document.getElementById(sectionId.replace('Upload', 'Btn').replace('upload', 'btn'));

            if (uploading) {
                section.classList.add('uploading');
                button.disabled = true;
                button.textContent = 'Uploading...';
            } else {
                section.classList.remove('uploading');
                button.disabled = false;
                button.textContent = sectionId === 'singleUpload' ? 'Upload Image' :
                                   sectionId === 'batchUpload' ? 'Upload Images' : 'Send Image';
            }
        }

        function updateProgress(sectionId, progress) {
            const progressContainer = document.getElementById(sectionId.replace('Upload', 'Progress'));
            const progressFill = document.getElementById(sectionId.replace('Upload', 'ProgressFill'));
            const progressText = document.getElementById(sectionId.replace('Upload', 'ProgressText'));

            progressContainer.style.display = 'block';
            progressFill.style.width = progress + '%';
            progressText.textContent = `Uploading... ${progress}%`;
        }

        function hideProgress(sectionId) {
            const progressContainer = document.getElementById(sectionId.replace('Upload', 'Progress'));
            setTimeout(() => {
                progressContainer.style.display = 'none';
            }, 1000);
        }

        async function uploadSingleImage() {
            if (!client) {
                showError('Please enter an authentication token first.');
                return;
            }

            const fileInput = document.getElementById('singleFile');
            const file = fileInput.files[0];

            if (!file) {
                showError('Please select a file first.');
                return;
            }

            setUploading('singleUpload', true);

            try {
                const result = await client.uploadImage(file, {
                    onProgress: (progress) => updateProgress('single', progress)
                });

                displayImageResult(result.data);
                showSuccess('Image uploaded successfully!');
                fileInput.value = '';

            } catch (error) {
                showError(`Upload failed: ${error.message}`);
            } finally {
                setUploading('singleUpload', false);
                hideProgress('single');
            }
        }

        async function uploadBatchImages() {
            if (!client) {
                showError('Please enter an authentication token first.');
                return;
            }

            const fileInput = document.getElementById('batchFiles');
            const files = Array.from(fileInput.files);

            if (files.length === 0) {
                showError('Please select files first.');
                return;
            }

            if (files.length > 5) {
                showError('Maximum 5 files allowed for batch upload.');
                return;
            }

            setUploading('batchUpload', true);

            try {
                const result = await client.uploadBatchImages(files, {
                    onProgress: (progress) => updateProgress('batch', progress)
                });

                result.forEach(imageData => displayImageResult(imageData));
                showSuccess(`${result.length} images uploaded successfully!`);
                fileInput.value = '';

            } catch (error) {
                showError(`Batch upload failed: ${error.message}`);
            } finally {
                setUploading('batchUpload', false);
                hideProgress('batch');
            }
        }

        async function sendChatImage() {
            if (!client) {
                showError('Please enter an authentication token first.');
                return;
            }

            const fileInput = document.getElementById('chatFile');
            const captionInput = document.getElementById('chatCaption');
            const file = fileInput.files[0];
            const caption = captionInput.value.trim();

            if (!file) {
                showError('Please select an image first.');
                return;
            }

            setUploading('chatUpload', true);

            try {
                const result = await client.sendChatImage('demo-channel', file, caption);

                displayChatMessage(result.message);
                showSuccess('Image sent to chat!');
                fileInput.value = '';
                captionInput.value = '';

            } catch (error) {
                showError(`Failed to send image: ${error.message}`);
            } finally {
                setUploading('chatUpload', false);
            }
        }

        function displayImageResult(imageData) {
            const imageDiv = document.createElement('div');
            imageDiv.className = 'image-result';

            imageDiv.innerHTML = `
                <img src="${imageData.thumbnail_url || imageData.secure_url}" alt="${imageData.filename}">
                <div class="image-info">
                    <strong>${imageData.filename}</strong><br>
                    ${imageData.width} × ${imageData.height}<br>
                    ${(imageData.bytes / 1024 / 1024).toFixed(2)} MB<br>
                    <small>Public ID: ${imageData.public_id}</small>
                </div>
            `;

            resultsDiv.appendChild(imageDiv);
        }

        function displayChatMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message';

            messageDiv.innerHTML = `
                <strong>You:</strong>
                <br>
                <img src="${message.thumbnail_url || message.file_url}" alt="Chat image">
                ${message.content ? `<div class="caption">${message.content}</div>` : ''}
                <small>${new Date().toLocaleTimeString()}</small>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Make functions globally available for onclick handlers
        window.uploadSingleImage = uploadSingleImage;
        window.uploadBatchImages = uploadBatchImages;
        window.sendChatImage = sendChatImage;
    </script>
</body>
</html>