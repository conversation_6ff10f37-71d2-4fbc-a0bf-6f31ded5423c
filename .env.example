# Server configuratie
PORT=8080
ALLOWED_ORIGINS=https://www.dekoninklijkeloop.nl,https://dekoninklijkeloop.nl

# Contact formulier email
ADMIN_EMAIL=<EMAIL>

# Inschrijvingen email
REGISTRATION_EMAIL=<EMAIL>

# SMTP configuratie voor algemene emails
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=password
SMTP_FROM=<EMAIL>

# SMTP configuratie voor registratie emails
REGISTRATION_SMTP_HOST=smtp.example.com
REGISTRATION_SMTP_PORT=587
REGISTRATION_SMTP_USER=<EMAIL>
REGISTRATION_SMTP_PASSWORD=password
REGISTRATION_SMTP_FROM=<EMAIL>

# SMTP configuratie voor Whisky for Charity
WFC_SMTP_HOST=arg-plplcl14.argewebhosting.nl
WFC_SMTP_PORT=465
WFC_SMTP_USER=<EMAIL>
WFC_SMTP_PASSWORD=your_password_here
WFC_SMTP_FROM=<EMAIL>
WFC_SMTP_SSL=true

# Rate limiting instellingen
EMAIL_RATE_LIMIT=10
CONTACT_RATE_LIMIT=5
REGISTRATION_RATE_LIMIT=3
RATE_LIMIT_WINDOW=3600

# Logging instellingen
LOG_LEVEL=info
LOG_FORMAT=json

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Template instellingen
TEMPLATE_DIR=./templates

# ELK Stack integratie (optioneel)
# ELK_ENDPOINT=http://localhost:9200
# ELK_INDEX=dkl-emails
# ELK_USERNAME=
# ELK_PASSWORD=

# Database configuratie
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=dklemailservice
DB_SSL_MODE=disable

# Overige configuratie
APP_ENV=development

# Notification settings
ENABLE_NOTIFICATIONS=true
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
NOTIFICATION_THROTTLE=15m
NOTIFICATION_MIN_PRIORITY=medium  # low, medium, high, critical

# Performance
EMAIL_BATCH_SIZE=50
EMAIL_BATCH_INTERVAL=15m
TEMPLATE_RELOAD_INTERVAL=1h
MAX_CONCURRENT_SENDS=10

# Automatische Email Ophaling
EMAIL_FETCH_INTERVAL=15
DISABLE_AUTO_EMAIL_FETCH=false

# Telegram Bot configuratie
ENABLE_TELEGRAM_BOT=false 
# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
CLOUDINARY_UPLOAD_FOLDER=dkl_images
CLOUDINARY_UPLOAD_PRESET=
CLOUDINARY_SECURE=true

# Test/Development Environment (separate credentials)
CLOUDINARY_TEST_CLOUD_NAME=test_cloud_name
CLOUDINARY_TEST_API_KEY=test_api_key
CLOUDINARY_TEST_API_SECRET=test_api_secret

# Redis Configuration
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
# Alternative: Use REDIS_URL for cloud providers (e.g., Render)
# REDIS_URL=redis://username:password@host:port/db