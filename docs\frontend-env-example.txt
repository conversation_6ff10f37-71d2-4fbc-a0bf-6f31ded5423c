# =============================================================================
# FRONTEND ENVIRONMENT CONFIGURATIE
# =============================================================================
# Kopieer dit bestand naar je FRONTEND project (niet backend!)
# en hernoem naar .env.development of .env.local afhankelijk van je framework
#
# Backend Status:
# - Lokaal:    http://localhost:8082
# - Productie: https://dklemailservice.onrender.com
# =============================================================================

# -----------------------------------------------------------------------------
# VOOR VITE/REACT PROJECTEN
# -----------------------------------------------------------------------------
# Hernoem naar: .env.development (local) of .env.production (prod)

# DEVELOPMENT (Lokaal)
VITE_API_BASE_URL=http://localhost:8082/api
VITE_WS_URL=ws://localhost:8082/api/chat/ws
VITE_ENV=development

# PRODUCTION (na hernoemen naar .env.production)
# VITE_API_BASE_URL=https://dklemailservice.onrender.com/api
# VITE_WS_URL=wss://dklemailservice.onrender.com/api/chat/ws
# VITE_ENV=production

# -----------------------------------------------------------------------------
# VOOR NEXT.JS PROJECTEN
# -----------------------------------------------------------------------------
# Hernoem naar: .env.local (local) of .env.production (prod)

# DEVELOPMENT (Lokaal)
NEXT_PUBLIC_API_BASE_URL=http://localhost:8082/api
NEXT_PUBLIC_WS_URL=ws://localhost:8082/api/chat/ws
NEXT_PUBLIC_ENV=development

# PRODUCTION (na hernoemen naar .env.production)
# NEXT_PUBLIC_API_BASE_URL=https://dklemailservice.onrender.com/api
# NEXT_PUBLIC_WS_URL=wss://dklemailservice.onrender.com/api/chat/ws
# NEXT_PUBLIC_ENV=production

# -----------------------------------------------------------------------------
# VOOR CREATE REACT APP PROJECTEN
# -----------------------------------------------------------------------------
# Hernoem naar: .env.development.local (local) of .env.production (prod)

# DEVELOPMENT (Lokaal)
REACT_APP_API_BASE_URL=http://localhost:8082/api
REACT_APP_WS_URL=ws://localhost:8082/api/chat/ws
REACT_APP_ENV=development

# PRODUCTION (na hernoemen naar .env.production)
# REACT_APP_API_BASE_URL=https://dklemailservice.onrender.com/api
# REACT_APP_WS_URL=wss://dklemailservice.onrender.com/api/chat/ws
# REACT_APP_ENV=production

# -----------------------------------------------------------------------------
# OPTIONELE CONFIGURATIE
# -----------------------------------------------------------------------------

# API timeout (milliseconds)
VITE_API_TIMEOUT=30000
NEXT_PUBLIC_API_TIMEOUT=30000
REACT_APP_API_TIMEOUT=30000

# Debug mode
VITE_DEBUG=true
NEXT_PUBLIC_DEBUG=true
REACT_APP_DEBUG=true

# =============================================================================
# INSTRUCTIES
# =============================================================================
# 
# 1. Kopieer dit bestand naar je FRONTEND project root
# 2. Hernoem naar .env.development (of .env.local voor Next.js)
# 3. Uncomment de regels die je framework gebruikt (Vite, Next.js, of CRA)
# 4. Voor productie: maak .env.production met productie URLs
# 5. Start frontend: npm run dev
# 6. Backend moet draaien op localhost:8082 (docker-compose -f docker-compose.dev.yml up -d)
#
# BELANGRIJK: 
# - .env.development wijst naar LOKALE backend (http://localhost:8082)
# - .env.production wijst naar PRODUCTIE backend (https://dklemailservice.onrender.com)
# - Git commit .env.production NIET (staat in .gitignore)
# =============================================================================