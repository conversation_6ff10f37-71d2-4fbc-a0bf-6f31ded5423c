package services

import (
	"context"
	"dklautomationgo/models"
	"dklautomationgo/repository"
	"fmt"
	"html/template"
	"strings"
	"time"

	"github.com/google/uuid"
)

// NotulenService handles business logic for notulen
type NotulenService struct {
	repo        *repository.PostgresNotulenRepository
	userService *AuthService
}

// NewNotulenService creates a new notulen service
func NewNotulenService(repo *repository.PostgresNotulenRepository, userService *AuthService) *NotulenService {
	return &NotulenService{
		repo:        repo,
		userService: userService,
	}
}

// CreateNotulen creates a new notulen document
func (s *NotulenService) CreateNotulen(ctx context.Context, userID uuid.UUID, req *models.NotulenCreateRequest) (*models.Notulen, error) {
	// Parse vergadering datum
	vergaderingDatum, err := time.Parse("2006-01-02", req.VergaderingDatum)
	if err != nil {
		return nil, fmt.Errorf("ongeldige vergadering datum: %w", err)
	}

	// Create notulen object
	notulen := &models.Notulen{
		ID:               uuid.New(),
		Titel:            req.Titel,
		VergaderingDatum: vergaderingDatum,
		Locatie:          req.Locatie,
		Voorzitter:       req.Voorzitter,
		Notulist:         req.Notulist,
		Aanwezigen:       req.Aanwezigen,
		Afwezigen:        req.Afwezigen,
		AgendaItems:      req.AgendaItems,
		Besluiten:        req.Besluiten,
		Actiepunten:      req.Actiepunten,
		Notities:         req.Notities,
		Status:           "draft",
		Versie:           1,
		CreatedBy:        userID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// Save to database
	if err := s.repo.Create(ctx, notulen); err != nil {
		return nil, fmt.Errorf("failed to create notulen: %w", err)
	}

	return notulen, nil
}

// GetNotulen retrieves a notulen by ID
func (s *NotulenService) GetNotulen(ctx context.Context, id uuid.UUID) (*models.Notulen, error) {
	return s.repo.GetByID(ctx, id)
}

// UpdateNotulen updates an existing notulen
func (s *NotulenService) UpdateNotulen(ctx context.Context, userID uuid.UUID, id uuid.UUID, req *models.NotulenUpdateRequest) (*models.Notulen, error) {
	// Get existing notulen
	notulen, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get notulen: %w", err)
	}
	if notulen == nil {
		return nil, fmt.Errorf("notulen not found")
	}

	// Check if user can edit (only creator or admin)
	if notulen.CreatedBy != userID {
		// TODO: Add admin check via permission service
		return nil, fmt.Errorf("geen toestemming om notulen te bewerken")
	}

	// Check if notulen is finalized
	if notulen.Status == "finalized" {
		return nil, fmt.Errorf("gefinaliseerde notulen kunnen niet worden bewerkt")
	}

	// Update fields
	if req.Titel != "" {
		notulen.Titel = req.Titel
	}
	if req.Locatie != "" {
		notulen.Locatie = req.Locatie
	}
	if req.Voorzitter != "" {
		notulen.Voorzitter = req.Voorzitter
	}
	if req.Notulist != "" {
		notulen.Notulist = req.Notulist
	}
	if req.Aanwezigen != nil {
		notulen.Aanwezigen = req.Aanwezigen
	}
	if req.Afwezigen != nil {
		notulen.Afwezigen = req.Afwezigen
	}
	if req.AgendaItems != nil {
		notulen.AgendaItems = req.AgendaItems
	}
	if req.Besluiten != nil {
		notulen.Besluiten = req.Besluiten
	}
	if req.Actiepunten != nil {
		notulen.Actiepunten = req.Actiepunten
	}
	if req.Notities != "" {
		notulen.Notities = req.Notities
	}

	notulen.UpdatedAt = time.Now()

	// Save changes
	if err := s.repo.Update(ctx, notulen); err != nil {
		return nil, fmt.Errorf("failed to update notulen: %w", err)
	}

	return notulen, nil
}

// FinalizeNotulen finalizes a notulen document
func (s *NotulenService) FinalizeNotulen(ctx context.Context, userID uuid.UUID, id uuid.UUID, req *models.NotulenFinalizeRequest) error {
	// Get existing notulen
	notulen, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get notulen: %w", err)
	}
	if notulen == nil {
		return fmt.Errorf("notulen not found")
	}

	// Check permissions
	if notulen.CreatedBy != userID {
		// TODO: Add admin check
		return fmt.Errorf("geen toestemming om notulen te finaliseren")
	}

	// Finalize
	return s.repo.Finalize(ctx, id, userID)
}

// ArchiveNotulen archives a notulen document
func (s *NotulenService) ArchiveNotulen(ctx context.Context, userID uuid.UUID, id uuid.UUID) error {
	// Get existing notulen
	notulen, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get notulen: %w", err)
	}
	if notulen == nil {
		return fmt.Errorf("notulen not found")
	}

	// Check permissions
	if notulen.CreatedBy != userID {
		// TODO: Add admin check
		return fmt.Errorf("geen toestemming om notulen te archiveren")
	}

	return s.repo.Archive(ctx, id)
}

// DeleteNotulen deletes a notulen (soft delete)
func (s *NotulenService) DeleteNotulen(ctx context.Context, userID uuid.UUID, id uuid.UUID) error {
	// Get existing notulen
	notulen, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get notulen: %w", err)
	}
	if notulen == nil {
		return fmt.Errorf("notulen not found")
	}

	// Check permissions
	if notulen.CreatedBy != userID {
		// TODO: Add admin check
		return fmt.Errorf("geen toestemming om notulen te verwijderen")
	}

	return s.repo.Delete(ctx, id)
}

// ListNotulen lists notulen with filtering and pagination
func (s *NotulenService) ListNotulen(ctx context.Context, filters *models.NotulenSearchFilters) ([]models.Notulen, int, error) {
	return s.repo.List(ctx, filters)
}

// SearchNotulen performs full-text search on notulen
func (s *NotulenService) SearchNotulen(ctx context.Context, query string, filters *models.NotulenSearchFilters) ([]models.Notulen, int, error) {
	return s.repo.Search(ctx, query, filters)
}

// GetNotulenVersions retrieves all versions of a notulen
func (s *NotulenService) GetNotulenVersions(ctx context.Context, notulenID uuid.UUID) ([]models.NotulenVersie, error) {
	return s.repo.GetVersions(ctx, notulenID)
}

// GetNotulenVersion retrieves a specific version of a notulen
func (s *NotulenService) GetNotulenVersion(ctx context.Context, notulenID uuid.UUID, versie int) (*models.NotulenVersie, error) {
	return s.repo.GetVersion(ctx, notulenID, versie)
}

// RenderMarkdown renders a notulen as Markdown
func (s *NotulenService) RenderMarkdown(notulen *models.Notulen) (string, error) {
	tmpl := template.Must(template.New("notulen").Parse(notulenMarkdownTemplate))

	var buf strings.Builder
	if err := tmpl.Execute(&buf, notulen); err != nil {
		return "", fmt.Errorf("failed to render markdown: %w", err)
	}

	return buf.String(), nil
}

// ValidateNotulen validates notulen data
func (s *NotulenService) ValidateNotulen(req *models.NotulenCreateRequest) error {
	if strings.TrimSpace(req.Titel) == "" {
		return fmt.Errorf("titel is verplicht")
	}
	if len(req.Titel) > 255 {
		return fmt.Errorf("titel mag niet langer zijn dan 255 karakters")
	}
	if req.VergaderingDatum == "" {
		return fmt.Errorf("vergadering datum is verplicht")
	}

	// Validate date format
	if _, err := time.Parse("2006-01-02", req.VergaderingDatum); err != nil {
		return fmt.Errorf("ongeldige vergadering datum formaat (gebruik YYYY-MM-DD)")
	}

	return nil
}

// Markdown template for notulen rendering
const notulenMarkdownTemplate = `# {{.Titel}}

**Datum:** {{.VergaderingDatum.Format "2 January 2006"}}
**Locatie:** {{.Locatie}}
**Voorzitter:** {{.Voorzitter}}
**Notulist:** {{.Notulist}}

## Aanwezigen
{{range .Aanwezigen}}- {{.}}
{{end}}

## Afwezigen
{{range .Afwezigen}}- {{.}}
{{end}}

## Agenda Items
{{range .AgendaItems}}
### {{.Titel}}
{{if .Beschrijving}}{{.Beschrijving}}{{end}}
{{if .Spreker}}**Spreker:** {{.Spreker}}{{end}}
{{if .Tijdslot}}**Tijdslot:** {{.Tijdslot}}{{end}}
{{end}}

## Besluiten
{{range .Besluiten}}
- {{.Beschrijving}}
  {{if .Verantwoordelijke}}**Verantwoordelijke:** {{.Verantwoordelijke}}{{end}}
  {{if .Deadline}}**Deadline:** {{.Deadline.Format "2-1-2006"}}{{end}}
{{end}}

## Actiepunten
{{range .Actiepunten}}
- [{{if eq .Status "completed"}}x{{else}} {{end}}] {{.Beschrijving}}
  **Verantwoordelijke:** {{.Verantwoordelijke}}
  {{if .Deadline}}**Deadline:** {{.Deadline.Format "2-1-2006"}}{{end}}
  **Status:** {{.Status}}
{{end}}

## Notities
{{.Notities}}

---
*Gemaakt op {{.CreatedAt.Format "2-1-2006 15:04"}} door {{.CreatedBy}}*
*Status: {{.Status}} | Versie: {{.Versie}}*
{{if .FinalizedAt}}*Gefinaliseerd op {{.FinalizedAt.Format "2-1-2006 15:04"}}*{{end}}`
